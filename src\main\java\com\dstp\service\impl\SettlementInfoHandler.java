package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.*;
import com.dstp.mapper.SettlementInfoBillMapper;
import com.dstp.mapper.SettlementInfoMapper;
import com.dstp.mapper.SettlementInfoPayMapper;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class SettlementInfoHandler extends MessageHandler {
    private final SettlementInfoMapper settlementInfoMapper;
    private final SettlementInfoBillMapper settlementInfoBillMapper;
    private final SettlementInfoPayMapper settlementInfoPayMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<Settlement> settlementList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(settlementList)) {
            log.warn("结算数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        settlementList = distinct(settlementList);

        // 1. 新增或更新结算信息
        settlementInfoMapper.batchUpsert(settlementList);

        // 2. 查询已存在的结算信息ID
        List<Long> settlementIds = batchGetParentPkId(settlementList, settlementInfoMapper);

        // 3. 保存新算账单明细
        List<SettlementInfoBill> settlementBillList = batchSetManyChildFkId(settlementList,
                Settlement::getBillDetails, SettlementInfoBill::setSettlementId);
        settlementInfoBillMapper.delete(new QueryWrapper<SettlementInfoBill>().in("settlement_id", settlementIds));
        settlementInfoBillMapper.batchInsert(settlementBillList);

        // 4. 保存新算支付明细
        List<SettlementInfoPay> settlementPayList = batchSetManyChildFkId(settlementList,
                Settlement::getPayDetails, SettlementInfoPay::setSettlementId);
        settlementInfoPayMapper.delete(new QueryWrapper<SettlementInfoPay>().in("settlement_id", settlementIds));
        settlementInfoPayMapper.batchInsert(settlementPayList);
    }
}
