package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 检务单证
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_inspection_doc")
public class InspectionLicence extends BaseEntity {
    @NotNull(message = "检务单ID不能为空")
    private Long inspectionInfoId;  // 关联主表ID
    @JsonProperty("gNum")
    private String gNum;            // 序号
    private String licenceType;
    private String licenceNo;
    @NotNull(message = "申报时间不能为空")
    private LocalDateTime declareTime;
    @NotNull(message = "出证时间不能为空")
    private LocalDateTime licenceTime;
}
