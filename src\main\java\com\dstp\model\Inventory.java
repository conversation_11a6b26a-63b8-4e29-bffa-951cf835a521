package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyLongDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 库存信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_stock_info")
public class Inventory extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId; // 企业 ID
    private String warehouseId; // 仓库 ID
    private String warehouseName; // 仓库名称
    private String goodsId; // 商品 ID
    private String goodsName; // 商品名称
    private String barCode; // 商品条码
    private String parentBrand; // 父级品牌
    private String brand; // 品牌
    private String firstCategory; // 一级分类
    private String secondCategory; // 二级分类
    private String thirdCategory; // 三级分类
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long price; // 单价（分）
    private String currency; // 币种
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long amount; // 总价（分）
    private String batchCode; // 批次号
    @NotNull(message = "入库日期不能为空")
    private LocalDateTime warehouseTime; // 入库日期
    private LocalDateTime productionTime; // 生产日期
    private LocalDateTime expireTime; // 失效日期
    @NotNull(message = "总库存不能为空")
    private Integer totalStockNum; // 总库存
    @NotNull(message = "真实库存不能为空")
    private Integer realStockNum; // 真实库存
    @NotNull(message = "冻结库存不能为空")
    private Integer realFrozenStockNum; // 冻结库存
    @NotNull(message = "锁定库存不能为空")
    private Integer lockStockNum; // 锁定库存
    @NotNull(message = "可用库存不能为空")
    private Integer usableStockNum; // 可用库存
    @JsonProperty("inventoryVersion")
    private String version; // 版本号
    private Integer stockType; // 库存类型: 0：好品 1：坏品
    @JsonProperty("inventoryDate")
    private LocalDateTime stockQueryTime; // 库存查询时间
    private Long sourceEntId; // 数据来源企业 ID
    private Long serverEntId; // 服务商企业 ID
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite; // 来源站点
    private String linkId; // 链路 ID
    private String dataSource; // 数据来源
}
