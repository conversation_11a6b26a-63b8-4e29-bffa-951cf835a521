package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.TallyGoods;
import com.dstp.mapper.TallyGoodsMapper;
import com.dstp.mapper.TallyMapper;
import com.dstp.model.Tally;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class TallyHandler extends MessageHandler {
    private final TallyMapper tallyMapper;
    private final TallyGoodsMapper tallyGoodsMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<Tally> tallyList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(tallyList)) {
            log.warn("理货数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        tallyList = distinct(tallyList);

        // 1. 新增或更新理货信息
        tallyMapper.batchUpsert(tallyList);

        // 2. 查询已存在的理货信息ID
        List<Long> tallyIds = batchGetParentPkId(tallyList, tallyMapper);

        // 3. 保存新理货商品信息
        List<TallyGoods> tallyGoodsList = batchSetManyChildFkId(tallyList,
                Tally::getGoodsList, TallyGoods::setTallyOrderId);
        tallyGoodsMapper.delete(new QueryWrapper<TallyGoods>().in("tally_order_id", tallyIds));
        tallyGoodsMapper.batchInsert(tallyGoodsList);
    }
}
