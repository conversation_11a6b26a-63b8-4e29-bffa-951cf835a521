package com.dstp.deserializer;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.math.BigDecimal;

@Slf4j
public class MoneyLongDeserializer extends JsonDeserializer<Long> {
    @Override
    public Long deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getText();

        // 将价格字符串转换为 BigDecimal
        BigDecimal decimalValue;
        try {
            decimalValue = new BigDecimal(value);
        } catch (NumberFormatException e) {
            log.error("无效的价格格式: {}, 期望格式为数字", value);
            throw new JsonParseException(p, "无效的价格格式: " + value, e);
        }

        // 乘以 100 转换为分
        return decimalValue.multiply(new BigDecimal("100")).longValue();

    }
}
