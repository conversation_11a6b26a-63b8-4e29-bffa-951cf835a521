package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

/**
 * To C 销售商品
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_sale_order_to_c_goods")
public class SaleToCGoods extends BaseEntity {
    @NotNull(message = "2C销售单ID不能为空")
    private Long saleOrderToCId;
    private String goodsName;
    private String barCode;
    @NotNull(message = "销售数量不能为空")
    private Integer qty;
    @TableField("category_1")
    private String category1;
    @TableField("category_2")
    private String category2;
}
