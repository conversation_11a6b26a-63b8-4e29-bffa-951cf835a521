package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyDecimalDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;

/**
 * 融资还款明细
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_refund_detail")
public class FinancingRefundDetail extends BaseEntity {
    @NotNull(message = "还款ID不能为空")
    private Long refundId;
    private String financingOrderNo;
    @NotNull(message = "还款本金不能为空")
    @JsonDeserialize(using = MoneyDecimalDeserializer.class)
    private BigDecimal refundPrincipal;  // 单位：分
    @NotNull(message = "还款利息不能为空")
    @JsonDeserialize(using = MoneyDecimalDeserializer.class)
    private BigDecimal refundInterest;   // 单位：分
}
