package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.CustomsInventoryGoods;
import com.dstp.mapper.CustomsInventoryGoodsMapper;
import com.dstp.mapper.CustomsInventoryMapper;
import com.dstp.model.CustomsInventory;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class CustomsInventoryHandler extends MessageHandler {
    private final CustomsInventoryMapper customsInventoryMapper;
    private final CustomsInventoryGoodsMapper customsInventoryGoodsMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<CustomsInventory> customsInventoryList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(customsInventoryList)) {
            log.warn("报关清单数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        customsInventoryList = distinct(customsInventoryList);

        // 1. 新增或更新报关清单
        customsInventoryMapper.batchUpsert(customsInventoryList);

        // 2. 查询已存在的报关清单ID
        List<Long> inventoryIds = batchGetParentPkId(customsInventoryList, customsInventoryMapper);

        // 3. 保存新报关清单商品
        List<CustomsInventoryGoods> customsInventoryGoodsList = batchSetManyChildFkId(customsInventoryList,
                CustomsInventory::getGoodsList, CustomsInventoryGoods::setInventoryId);
        customsInventoryGoodsMapper.delete(new QueryWrapper<CustomsInventoryGoods>().in("inventory_id", inventoryIds));
        customsInventoryGoodsMapper.batchInsert(customsInventoryGoodsList);
    }
}
