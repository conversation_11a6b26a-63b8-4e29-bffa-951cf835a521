package com.dstp.consumer;

import com.dstp.service.*;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class SingleMessageProcessor {
    private final MessageHandlerFactory messageHandlerFactory;
    private final KafkaMessageTransformer kafkaMessageTransformer;

    public void singleProcess(String topic, ConsumerRecord<String, String> message) {
        MessageHandler handler = messageHandlerFactory.getHandler(topic);
        if (handler != null) {
            JsonNode jsonNode = kafkaMessageTransformer.singleTransformer(message);
            if (jsonNode != null) handler.process(jsonNode);
        } else {
            log.warn("未知的Topic: {}", topic);
        }
    }
}
