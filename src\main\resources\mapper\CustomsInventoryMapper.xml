<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.CustomsInventoryMapper">
    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="customsInventoryMap" type="com.dstp.model.CustomsInventory">
        <id property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="invtNo" column="invt_no"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="customsInventoryMap">
        SELECT id,ent_id,invt_no
        FROM doc_customs_clearance_inventory
        WHERE (ent_id,invt_no) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.entId}, #{item.invtNo})
        </foreach>
    </select>
</mapper>