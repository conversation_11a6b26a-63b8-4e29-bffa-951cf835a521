package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 省电报关单商品
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("tb_transaction_customs_clearance_goods")
public class CustomsDeclareGoods extends BaseEntity {
    @NotNull(message = "报关单ID不能为空")
    private Long customsClearanceId;
    @JsonProperty("gNum")
    @NotBlank(message = "序号不能为空")
    private String gNum;
    private String barCode;
    private String entGoodsNo;
    private String entGoodsName;
    @NotBlank(message = "HSCODE不能为空")
    private String hsCode;
    private String ciqCode;
    private String ciqName;
    private String specDescription;
    @NotBlank(message = "原产国不能为空")
    private String orgCountry;
    @NotBlank(message = "最终目的国不能为空")
    private String destinationCountry;
    private String natureExemption;
    private String declareUnit;
    private BigDecimal declareQty;
    private String firstLegalUnit;
    private BigDecimal firstLegalQty;
    private String secondLegalUnit;
    private BigDecimal secondLegalQty;
    @NotBlank(message = "申报币制不能为空")
    private String declareCurrency;
    private BigDecimal declareUnitPrice;
    @JsonProperty("declareTotalPrice")
    private BigDecimal declarerTotalPrice;
    private BigDecimal grossWeight;
    private BigDecimal netWeight;
    @TableField("trade_subtotal_CNY")
    private BigDecimal tradeSubtotalCNY;
    @TableField("trade_subtotal_USD")
    private BigDecimal tradeSubtotalUSD;
    @TableField("customs_trade_subtotal_CNY")
    private BigDecimal customsTradeSubtotalCNY;
    @TableField("customs_trade_subtotal_USD")
    private BigDecimal customsTradeSubtotalUSD;
    private String contrItem;
    private String declGoodsEname;
    private String gUnit;
    private BigDecimal goodsTotalVal;
    private String tradeCurrName;
    private String ciqCurr;
    private String ciqCurrName;
    private String unit1;
    private String exgVersion;
    private String exgNo;
    private String destinationCountryName;
    private String unit2;
    private String cusOriginCountryName;
    private String ciqOriginCountry;
    private String rcepOrigPlaceCode;
    private String rcepOrigPlaceCodeName;
    private String origPlaceCode;
    private String origPlaceCodeName;
    private String districtCode;
    private String districtCodeName;
    private String ciqDestCode;
    private String ciqDestCodeName;
    private String dutyMode;
    private String dutyModeName;
    private String cusSupvDmd;
    private String supvDmd;
    private String goodsTargetInput;
    private String stuff;
    private LocalDateTime prodValidDt;
    private String prodQgp;
    private String engManEntCnm;
    private String goodsModel;
    private String goodsBrand;
    private String produceDate;
    private String prodBatchNo;
    private String mnufctrRegNo;
    private String goodsAttr;
    private String goodsAttrName;
    private String purpose;
    private String purposeName;
    private String dangerGoodsSpec;
    private String dangerFlag;
    private String noDangFlag;
    private String noDangFlagName;
    private String uncode;
    private String dangName;
    private String packType;
    private String packTypeName;
    private String packSpec;
    private String useTo;
    private String typistNo;
    private String createUser;
    private LocalDateTime inDbTime;
    private String updateUser;
    private LocalDateTime updateTime;
    private String cusCiqNo;
    private String goodsSpec;
}
