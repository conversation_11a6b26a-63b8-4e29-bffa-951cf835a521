package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.PurchaseGoods;
import com.dstp.mapper.PurchaseGoodsMapper;
import com.dstp.mapper.PurchaseMapper;
import com.dstp.model.Purchase;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class PurchaseHandler extends MessageHandler {
    private final PurchaseMapper purchaseMapper;
    private final PurchaseGoodsMapper purchaseGoodsMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<Purchase> purchaseList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(purchaseList)) {
            log.warn("采购数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        purchaseList = distinct(purchaseList);

        // 1. 新增或更新采购信息
        purchaseList.forEach(purchase -> purchase.setDataSource("DISTRIBUTORS"));
        purchaseMapper.batchUpsert(purchaseList);

        // 2. 查询已存在的保税电商交运ID
        List<Long> purchaseIds = batchGetParentPkId(purchaseList, purchaseMapper);

        // 3. 保存新采购商品信息
        List<PurchaseGoods> purchaseGoodsList = batchSetManyChildFkId(purchaseList,
                Purchase::getGoodsList, PurchaseGoods::setPurchaseOrderId);
        purchaseGoodsMapper.delete(new QueryWrapper<PurchaseGoods>().in("purchase_order_id", purchaseIds));
        purchaseGoodsMapper.batchInsert(purchaseGoodsList);
    }
}
