package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * To B 销售
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_sale_order_to_b")
public class SaleToB extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    @NotBlank(message = "企业订单编号不能为空")
    private String entOrderNo;
    @NotNull(message = "订单时间不能为空")
    private LocalDateTime orderTime;
    private String contractNo;
    private String poOrderNo;
    private String merchant;
    private String wmsOrderNo;
    private String financingOrderNo;
    private LocalDateTime outboundTime;
    @NotNull(message = "销售总数量不能为空")
    private Integer salesQty;
    private BigDecimal salesAmount;
    private String currency;
    private String orderStatus;
    private Long sourceEntId;
    private Long serverEntId;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    private String linkId;
    @TableField(exist = false, select = false)
    private List<SaleToBGoods> goodsList;
    @TableField(exist = false, select = false)
    private List<SaleToBPurchase> purchaseList;

    public String getUniqueKeyString() {
        if (entId == null || entOrderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + entOrderNo + ":" + sourceSite;
    }
}
