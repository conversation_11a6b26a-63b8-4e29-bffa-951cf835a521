package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 国内运输货柜
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_internal_transport_container")
public class InternalTransportContainer extends BaseEntity {
    @NotNull(message = "运输单ID不能为空")
    private Long orderId;
    @JsonProperty("gNum")
    private String gNum;
    private String containerNo;
    private String containerModel;
    private String sealNo;
    private Integer cabinets;
    private String notes;
}