<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.StorageVoucherMapper">
    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="storageVoucherMap" type="com.dstp.model.StorageVoucher">
        <id property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="stackingVoucherCode" column="stacking_voucher_code"/>
        <result property="sourceSite" column="source_site"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="storageVoucherMap">
        SELECT id,ent_id,stacking_voucher_code,source_site
        FROM doc_storage_voucher
        WHERE (ent_id,stacking_voucher_code,source_site) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.entId}, #{item.stackingVoucherCode}, #{item.sourceSite})
        </foreach>
    </select>
</mapper>