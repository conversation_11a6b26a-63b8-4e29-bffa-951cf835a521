<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.CheckMapper">
    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="checkMap" type="com.dstp.model.Check">
        <id property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="checkNo" column="check_no"/>
        <result property="sourceSite" column="source_site"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="checkMap">
        SELECT id,ent_id,check_no,source_site
        FROM doc_check_order
        WHERE (ent_id,check_no,source_site) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.entId}, #{item.checkNo}, #{item.sourceSite})
        </foreach>
    </select>
</mapper>