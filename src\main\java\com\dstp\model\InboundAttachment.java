package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 入库单证
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_inbound_attachment")
public class InboundAttachment extends BaseEntity {
    @NotNull(message = "入库单ID不能为空")
    private Long inboundOrderId;
    @JsonProperty("gNum")
    private String gNum;
    private String docType;
    private String docUrl;
    private String notes;
}