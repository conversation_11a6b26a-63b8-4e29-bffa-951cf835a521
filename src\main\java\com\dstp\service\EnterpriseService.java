package com.dstp.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.mapper.EnterpriseMapper;
import com.dstp.model.Enterprise;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业服务
 * 提供企业信息查询和缓存管理
 */
@Service
@Slf4j
@RequiredArgsConstructor
@CacheConfig(cacheNames = "enterpriseCache", cacheManager = "enterpriseCacheManager")
public class EnterpriseService extends BaseCacheService<Enterprise, String> {

    private final EnterpriseMapper enterpriseMapper;
    private final CacheManager enterpriseCacheManager;

    /**
     * 根据主数据编码获取企业信息
     * 使用缓存提高查询性能
     *
     * @param mdmCode 主数据编码
     * @return 企业信息
     */
    @Cacheable(key = "#mdmCode", condition = "#mdmCode != null", unless = "#result == null")
    public Enterprise getEnterpriseByMdmCode(String mdmCode) {
        if (StringUtils.isBlank(mdmCode)) {
            return null;
        }

        log.debug("缓存未命中，从数据库查询企业信息: 主数据编码={}", mdmCode);
        return enterpriseMapper.selectOne(new QueryWrapper<Enterprise>()
                .select("mdm_code", "id", "name", "license_no")
                .eq("mdm_code", mdmCode));
    }

    /**
     * 根据主数据编码列表获取企业信息映射
     * 使用缓存提高查询性能
     * 优化批量查询逻辑，避免逐个查询
     *
     * @param mdmCodes 主数据编码列表
     * @return 企业信息映射
     */
    public Map<String, Enterprise> getEnterpriseByMdmCodeList(List<String> mdmCodes) {
        if (CollectionUtils.isEmpty(mdmCodes)) {
            return new HashMap<>();
        }

        // 去重处理并标准化条形码
        List<String> distinctMdmCodes = mdmCodes.stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();

        return batchGet(distinctMdmCodes);
    }

    @Override
    protected CacheManager getCacheManager() {
        return enterpriseCacheManager;
    }

    @Override
    protected String getCacheName() {
        return "enterpriseCache";
    }

    @Override
    protected String getCacheStatsPrefix() {
        return "企业";
    }

    @Override
    protected List<Enterprise> batchQueryFromDb(List<String> keys) {
        return enterpriseMapper.selectList(new QueryWrapper<Enterprise>()
                .select("mdm_code", "id", "name", "license_no")
                .in("mdm_code", keys));
    }

    @Override
    protected String getKey(Enterprise obj) {
        return obj.getMdmCode();
    }
}