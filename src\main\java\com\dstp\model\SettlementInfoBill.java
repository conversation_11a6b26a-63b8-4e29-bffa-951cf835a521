package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;

/**
 * 结算信息-账单明细
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_settlement_info_bill")
public class SettlementInfoBill extends BaseEntity {
    @NotNull(message = "结算单ID不能为空")
    private Long settlementId; // 关联字段：doc_settlement_info.id
    private String sourceOrder;
    @NotBlank(message = "费项名称不能为空")
    private String projectName;
    @NotNull(message = "应结算金额不能为空")
    private BigDecimal amount;
}
