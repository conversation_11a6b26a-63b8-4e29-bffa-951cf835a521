package com.dstp.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public abstract class BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    @NotNull(message = "创建时间不能为空")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    @NotBlank(message = "创建人不能为空")
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    @NotNull(message = "修改时间不能为空")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedAt;
    @NotBlank(message = "修改人不能为空")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String modifiedBy;
    @TableField(exist = false)
    private transient Long kafkaOffset;

    public String getUniqueKeyString() {
        return id + "";
    }
}
