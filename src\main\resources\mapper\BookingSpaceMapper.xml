<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.BookingSpaceMapper">
    <select id="selectByUnique" resultType="com.dstp.model.BookingSpace">
        select * from doc_booking_space
        <where>
            <if test="entId != null">AND ent_id = #{entId}</if>
            <if test="soNo != null">AND so_no = #{soNo}</if>
            <if test="soNo == null">AND so_no IS NULL</if>
            <if test="billNo != null">AND bill_no = #{billNo}</if>
            <if test="billNo == null">AND bill_no IS NULL</if>
            <if test="containerNo != null">AND container_no = #{containerNo}</if>
            <if test="containerNo == null">AND container_no IS NULL</if>
        </where>
    </select>

    <update id="updateByUnique" parameterType="com.dstp.model.BookingSpace">
        UPDATE doc_booking_space SET
        ent_id=#{entId},
        so_no=#{soNo},
        customer_code=#{customerCode},
        entrust_name=#{entrustName},
        shipper=#{shipper},
        consignee=#{consignee},
        carrier_code=#{carrierCode},
        vessel=#{vessel},
        book_date=#{bookDate},
        pol_name=#{polName},
        enter_date=#{enterDate},
        etd_atd=#{etdAtd},
        pod_name=#{podName},
        eta_ata=#{etaAta},
        bill_no=#{billNo},
        sign_date=#{signDate},
        source_ent_id=#{sourceEntId},
        port_code=#{portCode},
        ieid=#{ieid},
        modified_at=#{modifiedAt},
        modified_by=#{modifiedBy},
        source_site=#{sourceSite},
        data_sources=#{dataSources},
        entry_no=#{entryNo},
        container_type=#{containerType},
        container_no=#{containerNo},
        seal_no=#{sealNo}
        <where>
            <if test="entId != null">AND ent_id = #{entId}</if>
            <if test="soNo != null">AND so_no = #{soNo}</if>
            <if test="soNo == null">AND so_no IS NULL</if>
            <if test="billNo != null">AND bill_no = #{billNo}</if>
            <if test="billNo == null">AND bill_no IS NULL</if>
            <if test="containerNo != null">AND container_no = #{containerNo}</if>
            <if test="containerNo == null">AND container_no IS NULL</if>
        </where>
    </update>
</mapper>