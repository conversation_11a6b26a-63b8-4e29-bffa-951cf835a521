<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.GoodsMapper">
    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="goodsMap" type="com.dstp.model.Goods">
        <id property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="entGoodsNo" column="ent_goods_no"/>
        <result property="barCode" column="bar_code"/>
        <result property="brand" column="brand"/>
        <result property="parentBrand" column="parent_brand"/>
        <result property="price" column="price"/>
        <result property="firstCategory" column="first_category"/>
        <result property="secondCategory" column="second_category"/>
        <result property="thirdCategory" column="third_category"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="goodsMap">
        SELECT id, ent_id, ent_goods_no,bar_code, brand, parent_brand, price, first_category, second_category, third_category
        FROM doc_goods
        WHERE (ent_id,ent_goods_no) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.entId}, #{item.entGoodsNo})
        </foreach>
    </select>
</mapper>