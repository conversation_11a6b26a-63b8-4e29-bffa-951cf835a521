package com.dstp.consumer;

import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Component
public class BatchProcessorMetrics {
    private final MeterRegistry meterRegistry;
    private final Map<String, Timer> topicProcessingTimers = new ConcurrentHashMap<>();
    private final Map<String, Gauge> topicQueueSizeGauges = new ConcurrentHashMap<>();
    
    public BatchProcessorMetrics(MeterRegistry meterRegistry, BatchMessageProcessor processor) {
        this.meterRegistry = meterRegistry;
        
        // 注册队列大小监控
        Gauge.builder("batch.processor.total.queue.size")
            .description("批处理器总队列大小")
            .register(meterRegistry, processor, BatchMessageProcessor::getTotalQueueSize);
    }
    
    public void recordProcessingTime(String topic, long processingTimeMs) {
        Timer timer = topicProcessingTimers.computeIfAbsent(topic, 
            t -> Timer.builder("batch.processor.processing.time")
                .tag("topic", t)
                .description("Topic处理时间")
                .register(meterRegistry));
        timer.record(processingTimeMs, TimeUnit.MILLISECONDS);
    }
    
    public void recordQueueSize(String topic, int size) {
        topicQueueSizeGauges.computeIfAbsent(topic,
            t -> Gauge.builder("batch.processor.queue.size")
                .tag("topic", t)
                .description("Topic队列大小")
                .register(meterRegistry, () -> getCurrentQueueSize(t)));
    }
}
