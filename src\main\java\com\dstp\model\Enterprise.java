package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

/**
 * 企业信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("tb_enterprise")
public class Enterprise extends BaseEntity {
    private String mdmCode; // 主数据编码
    private String name; // 企业名称
    private String englishName; // 企业英文名称
    private String shortName; // 企业简称
    private String type; // 企业类型（境内：RESIDENT，境外：NON_RESIDENT）
    private String licenseNo; // 营业证件号
    private String licenseType; // 营业证件类型（工商注册：GENERAL，统一信用代码：CONSOLIDATED，香港工商注册：NON_RESIDENT）
    private String status; // 状态（启用：ENABLED，禁用：DISABLED）
    private String clientType; // 客户类型（客户：CLIENT，供应商：SUPPLIER，供应商&客户：ALL）
    private String country; // 所属国
    private String clientStatus; // 客户状态（锁定：LOCKING，有效：EFFECTIVE）
    private String internalFlag; // 企业是否为内部企业（外部企业：EXTERNAL_ENTERPRISE，内部企业：INSIDE_ENTERPRISE）
    private String joinSite; // 站点（青岛：QINGDAO，南沙：NANSHA）
    private Integer brandFlag; // 品牌方企业标识（1：品牌方，0：非品牌方）
    private String traceSourceCode; // 溯源系统登记号
    private String legalRepresentative; // 法人
    private String establishmentDate; // 成立时间
    private Long relatedEntId; // 境外企业关联的境内企业 ID
    private String channelSource; // 来源渠道
    private Boolean publishConfirm; // 是否确认发布（1为true，0为false，该字段只针对中标信息）
}
