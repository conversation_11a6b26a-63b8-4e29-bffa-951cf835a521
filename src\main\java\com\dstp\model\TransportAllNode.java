package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.List;

/**
 * 运输节点信息 - 所有节点
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_transport_info_all_node")
public class TransportAllNode extends BaseEntity {
    @NotNull(message = "运输节点信息ID不能为空")
    private Long transportId; // 关联字段：doc_transport_info.id
    private String taskCode; // 任务编号
    @TableField(exist = false, select = false)
    private List<TransportAllNodeOp> operation; // 一对多关联：操作节点

    public String getUniqueKeyString() {
        return transportId + ":" + taskCode;
    }
}
