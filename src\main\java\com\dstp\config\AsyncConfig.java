package com.dstp.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 异步处理配置
 * 用于配置异步任务执行器
 */
@Configuration
@EnableAsync
public class AsyncConfig {

    /**
     * 异常处理专用线程池
     * 用于异步处理异常日志和告警
     */
    @Bean(name = "exceptionExecutor")
    public Executor exceptionExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("exception-async-");
        executor.initialize();
        return executor;
    }
}
