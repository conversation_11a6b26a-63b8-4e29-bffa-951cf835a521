package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyLongDeserializer;
import com.dstp.deserializer.WeightDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;

/**
 * 核注单商品
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_check_order_goods")
public class CheckGoods extends BaseEntity {
    @NotNull(message = "核注单ID不能为空")
    private Long orderId;  // 关联主表ID
    @JsonProperty("gNum")
    private String gNum;
    private String barCode;
    private String entGoodsNo;
    private String entGoodsName;
    private String hsCode;
    private String specDescription;
    private String orgCountry;
    private String destinationCountry;
    private String natureExemption;
    private String declareUnit;
    private BigDecimal declareQty;
    private String firstLegalUnit;
    private BigDecimal firstLegalQty;
    private String secondLegalUnit;
    private BigDecimal secondLegalQty;
    private String declareCurrency;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long declareUnitPrice;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long declareTotalPrice;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal grossWeight;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal netWeight;
}
