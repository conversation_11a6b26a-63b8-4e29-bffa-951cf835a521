package com.dstp.model;

import java.io.Serial;

/**
 * 三元组实现
 */
public class Tuple3<T0, T1, T2> extends Tuple {

    @Serial
    private static final long serialVersionUID = 1L;

    public T0 f0;
    public T1 f1;
    public T2 f2;

    public Tuple3() {
    }

    public Tuple3(T0 f0, T1 f1, T2 f2) {
        this.f0 = f0;
        this.f1 = f1;
        this.f2 = f2;
    }

    @Override
    public int getArity() {
        return 3;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getField(int pos) {
        return switch (pos) {
            case 0 -> (T) f0;
            case 1 -> (T) f1;
            case 2 -> (T) f2;
            default -> throw new IndexOutOfBoundsException("索引越界: " + pos);
        };
    }

    @Override
    @SuppressWarnings("unchecked")
    protected <T> void setField(int pos, T value) {
        switch (pos) {
            case 0: f0 = (T0) value; break;
            case 1: f1 = (T1) value; break;
            case 2: f2 = (T2) value; break;
            default: throw new IndexOutOfBoundsException("索引越界: " + pos);
        }
    }
}
