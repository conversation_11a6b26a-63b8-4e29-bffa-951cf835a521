package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_purchase_order")
public class Purchase extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    @NotBlank(message = "企业订单编号不能为空")
    private String entOrderNo;
    @NotNull(message = "订单时间不能为空")
    private LocalDateTime orderTime;
    private String contractNo;
    private String poOrderNo;
    private String merchant;
    private String financingOrderNo;
    @NotNull(message = "采购数量不能为空")
    private BigDecimal purchaseQty;
    private BigDecimal purchaseAmount;
    private String currency;
    private LocalDateTime payTime;
    private String wmsOrderNo;
    private LocalDateTime inboundTime;
    private String orderStatus;
    private Long sourceEntId;
    private Long serverEntId;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    @NotBlank(message = "数据来源不能为空")
    private String dataSource;
    private String linkId;
    @TableField(exist = false, select = false)
    private List<PurchaseGoods> goodsList;

    public String getUniqueKeyString() {
        if (entId == null || entOrderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + entOrderNo + ":" + sourceSite;
    }
}