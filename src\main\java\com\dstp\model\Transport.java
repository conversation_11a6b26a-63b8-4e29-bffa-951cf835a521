package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 运输节点信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_transport_info")
public class Transport extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId; // 企业 ID
    @NotBlank(message = "订单号不能为空")
    private String statementNum; // 订单号
    private String logisticsNo; // 运输单号
    private String driverName; // 司机名
    private String phone; // 司机电话
    @NotBlank(message = "车牌号不能为空")
    private String vehicleNum; // 车牌
    private String vehicleTypeName; // 车型
    @NotBlank(message = "客户编码不能为空")
    private String customerCode; // 客户编码
    private String supplierName; // 供应商名称
    private String pickupPlace; // 提地
    private String sendPlace; // 送地
    private BigDecimal totalWeight; // 总重量
    private Integer totalNum; // 总件数
    private String containerNum; // 柜号
    private String timeStamp; // 请求时间戳
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite; // 来源站点
    private Long sourceEntId; // 数据来源企业 ID
    @TableField(exist = false, select = false)
    private List<TransportAllNode> allNode; // 一对多关联：所有节点信息
    @TableField(exist = false, select = false)
    private List<TransportNode> node; // 一对多关联：节点信息

    public String getUniqueKeyString() {
        return entId + ":" + statementNum;
    }
}
