<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.FinancingRefundMapper">
    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="financingRefundMap" type="com.dstp.model.FinancingRefund">
        <id property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="refundOrderNo" column="refund_order_no"/>
        <result property="sourceSite" column="source_site"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="financingRefundMap">
        SELECT id,ent_id,refund_order_no,source_site
        FROM doc_refund_info
        WHERE (ent_id,refund_order_no,source_site) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.entId}, #{item.refundOrderNo}, #{item.sourceSite})
        </foreach>
    </select>
</mapper>