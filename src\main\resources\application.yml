spring:
  application:
    name: dxc_sync_gel
  profiles:
    active: ${SPRING_PROFILES_ACTIVE}

  datasource:
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      auto-commit: false
      data-source-properties:
        rewriteBatchedStatements: true # 启用批量语句重写（提升saveBatch性能10倍+）
      # 连接池基础配置
      maximum-pool-size: 20          # 最大连接数（默认 10，根据并发量调整）
      minimum-idle: 10               # 最小空闲连接数（建议与 maximum-pool-size 一致或更低）
      idle-timeout: 60000            # 空闲连接超时时间（毫秒，默认 600000，建议调低避免过多空闲连接）
      max-lifetime: 1800000          # 连接最大生命周期（毫秒，默认 1800000，建议 30 分钟）
      connection-timeout: 5000       # 获取连接的超时时间（默认 30000，避免过长等待）

      # 性能优化配置
      connection-test-query: SELECT 1   # 数据库心跳检测 SQL（某些驱动需配置，如 MySQL）
      validation-timeout: 5000          # 心跳检测超时时间（毫秒）

      # 连接保活配置
      keepalive-time: 30000             # 保活心跳间隔（毫秒，默认 0，建议 30 秒）
      initialization-fail-timeout: 1    # 连接池初始化失败超时（默认 1，快速失败）

#  cache:
#    type: CAFFEINE # 使用Caffeine缓存
#    caffeine:
#      spec: maximumSize=1000,expireAfterAccess=3600s

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS}
    consumer:
      topic: import_export_logistics_info
      # topic: dxc_newly_tech_transport_info,dxc_logistics_pod_order,dxc_logistics_customs_clearance,dxc_logistics_transport,dxc_logistics_check_order,dxc_logistics_inbound_order,dxc_logistics_tally_order,dxc_logistics_outbound_order,dxc_logistics_bonded_ec_order,dxc_logistics_bonded_ec_logistics,dxc_logistics_storage_voucher,dxc_logistics_inspection_info,dxc_trade_purchase,dxc_trade_sale_tob,dxc_trade_sale_toc,dxc_financial_info,dxc_financial_repayment_info,dxc_info_inventory_query,dxc_financial_credit_info,dxc_trade_synchronize_goods,dxc_logistics_goods,dxc_logistics_inventory_snapshot,dxc_logistics_booking_space,dxc_trade_settlement_info,dxc_logistics_inventory_information,import_export_logistics_info
      client-id: dxc_sync_gel
      group-id: dxc_sync_gel
      auto-offset-reset: earliest
      enable-auto-commit: false
      security:
        protocol: SASL_PLAINTEXT
      properties:
        enable.auto.commit: false
        max-poll-records: 500
        fetch.max.wait.ms: 500
        session.timeout.ms: 30000
        request.timeout.ms: 600000
        max.poll.interval.ms: 300000
        sasl:
          mechanism: PLAIN
          jaas:
            config: org.apache.kafka.common.security.plain.PlainLoginModule required username="${KAFKA_USERNAME}" password="${KAFKA_PASSWORD}";
    listener:
      ack-mode: manual_immediate # Listener AckMode. See the spring-kafka documentation.
      type: single # Listener type. batch|single
      missing-topics-fatal: false
      concurrency: 4

# 缓存配置
cache:
  default:
    max-size: 1000
    expire-after-access: 3600  # 1小时
  enterprise:
    max-size: 500
    expire-after-access: 7200  # 2小时
  goods:
    max-size: 2000
    expire-after-access: 3600  # 1小时
  warehouse:
    max-size: 200
    expire-after-access: 7200  # 2小时
  exchange-rate:
    max-size: 100
    expire-after-access: 86400 # 24小时

# 批处理配置
batch:
  processor:
    max-batch-size: 500        # 单批次最大消息数
    initial-delay-seconds: 5   # 初始延迟时间
    fixed-delay-seconds: 10    # 固定间隔时间
    thread-pool-size: 4        # 处理线程池大小

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: auto
      insert-strategy: not_null    # 控制INSERT操作字段策略（只插入非空字段）
      update-strategy: not_null    # 控制UPDATE操作字段策略（只更新非空字段）

# 企业微信机器人 Webhook
wechat:
  webhook:
    url: ${WE_CHAT_WEBHOOK_URL}
  alarm:
    enabled: false

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics,caches
  endpoint:
    health:
      show-details: always
    prometheus:
      access: read_only
  metrics:
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.75, 0.95, 0.99
  prometheus:
    metrics:
      export:
        enabled: true

#logging:
#  level:
#    com.dstp: INFO
#    # 关键业务模块
#    com.dstp.consumer: ERROR
#    com.dstp.service: ERROR
#    # 数据库访问层
#    com.dstp.repository: ERROR       # MyBatis Mapper接口
#    org.jdbc: ERROR                 # JDBC核心
#    com.zaxxer.hikari: WARN         # 连接池
#    # 第三方库控制
#    org.springframework: WARN
#    org.hibernate: ERROR