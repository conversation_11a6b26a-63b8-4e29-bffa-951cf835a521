package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyLongDeserializer;
import com.dstp.deserializer.WeightDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 保税电商订单
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_bonded_ec_order")
public class BondedEcOrder extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    private String busiOrderNo;
    private String serviceOrderNo;
    private String declareCode;
    private String busiType;
    private String clearanceType;
    @NotBlank(message = "企业订单编号不能为空")
    private String entOrderNo;
    private LocalDateTime orderDate;
    private String warehouseId;
    private String thirdLogisticsCode;
    private Integer orderType;
    private String electricName;
    private String cbepcomName;
    private String electricCode;
    private String cbepcomCode;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long shippingFee;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long taxFee;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal grossWeight;
    private String notes;
    private Long sourceEntId;
    private Long serverEntId;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    private String linkId;
    // 一对一关联地址信息
    @TableField(exist = false, select = false)
    private BondedEcOrderAddress recipient;
    // 一对多关联商品列表
    @TableField(exist = false, select = false)
    @JsonProperty("goodList")
    private List<BondedEcOrderGoods> goodsList;

    @Override
    public String getUniqueKeyString() {
        if (entId == null || entOrderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + entOrderNo + ":" + sourceSite;
    }
}