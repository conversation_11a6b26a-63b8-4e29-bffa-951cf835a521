package com.dstp.service.impl;

import com.dstp.mapper.FinancingApplyMapper;
import com.dstp.model.FinancingApply;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class FinancingApplyHandler extends MessageHandler {
    private final FinancingApplyMapper financingApplyMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<FinancingApply> financingApplyList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(financingApplyList)) {
            log.warn("融资申请数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        financingApplyList = distinct(financingApplyList);
        // 1. 新增或更新融资申请
        financingApplyMapper.batchUpsert(financingApplyList);
    }
}
