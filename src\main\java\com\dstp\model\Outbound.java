package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyLongDeserializer;
import com.dstp.deserializer.WeightDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 出库信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_outbound_header")
public class Outbound extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    @NotBlank(message = "出库单号不能为空")
    private String entOrderNo;
    private String busiOrderNo;
    private String serviceOrderNo;
    private String clientOrderNo;
    private LocalDateTime commitTime;
    private String warehouseId;
    @JsonProperty("warehouseName")
    private String warehouse;
    private String bookNo;
    private String outputType;
    private String consigneeCode;
    private String consigneeName;
    private String logisticsNo;
    private String vehicleNo;
    @JsonProperty("superviseType")
    private String supervisionType;
    private LocalDateTime actualTime;
    private LocalDateTime requirementTime;
    private String declareCode;
    private String entryExitCode;
    @JsonProperty("relateBookCode")
    private String relateBookNo;
    private String operateCode;
    private String operateName;
    private String foreignConsignee;
    private String transportMode;
    private String transportTool;
    private String voyageNo;
    private String billNo;
    @JsonProperty("customsClearanceCode")
    private String clearanceCompanyCode;
    @JsonProperty("customsClearanceName")
    private String clearanceCompany;
    private String contractNo;
    private String checkNo;
    private String relateCheckNo;
    @JsonProperty("entryNo")
    private String custDeclrNo;
    private String inOutOrderNo;
    private String currency;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long shippingFee;
    @JsonProperty("premiumFee")
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long premium;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long diverseFee;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long amount;
    private BigDecimal declareQty;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal grossWeight;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal netWeight;
    private LocalDateTime orderCreateTime;
    private LocalDateTime deliverTime;
    private LocalDateTime declareTime;
    private LocalDateTime releaseTime;
    private LocalDateTime carrierTime;
    private LocalDateTime signTime;
    private String brand;
    private Long sourceEntId;
    private Long serverEntId;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    private String linkId;
    @TableField(exist = false, select = false)
    private List<OutboundGoods> goodsList;
    @TableField(exist = false, select = false)
    private List<OutboundSplint> splintList;
    @TableField(exist = false, select = false)
    private List<OutboundVehicle> vehicleList;

    public String getUniqueKeyString() {
        if (entId == null || entOrderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + entOrderNo + ":" + sourceSite;
    }
}