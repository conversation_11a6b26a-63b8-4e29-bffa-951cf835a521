package com.dstp.mapper;

import com.dstp.model.CommonGoods;
import com.dstp.model.CommonGoodsAttr;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface CommonGoodsMapper extends MyBaseMapper<CommonGoods> {

    @Select("""
            select
              a.goods_bar_code,
              a.standard_brand_name,
              b.name as first_category,
              c.name as second_category,
              d.name as third_category
            from gml_base.common_goods_info a
            left join gml_base.category_info b on a.category_level1 = b.id
            left join gml_base.category_info c on a.category_level2 = c.id
            left join gml_base.category_info d on a.category_level3 = d.id
            where a.goods_bar_code = #{barcode}
            """)
    @Results({
            @Result(property = "barCode", column = "goods_bar_code"),
            @Result(property = "brand", column = "standard_brand_name"),
            @Result(property = "firstCategory", column = "first_category"),
            @Result(property = "secondCategory", column = "second_category"),
            @Result(property = "thirdCategory", column = "third_category")
    })
    CommonGoodsAttr selectGoodsAttrByBarcode(@Param("barcode") String barcode);

    @Select("""
            <script>
                select
                    a.goods_bar_code,
                    a.standard_brand_name,
                    b.name as first_category,
                    c.name as second_category,
                    d.name as third_category
                from gml_base.common_goods_info a
                left join gml_base.category_info b on a.category_level1 = b.id
                left join gml_base.category_info c on a.category_level2 = c.id
                left join gml_base.category_info d on a.category_level3 = d.id
                where a.goods_bar_code in
                <foreach item='barcode' index='index' collection='barcodes' open='(' separator=',' close=')'>
                    #{barcode}
                </foreach>
            </script>
            """)
    @Results({
            @Result(property = "barCode", column = "goods_bar_code"),
            @Result(property = "brand", column = "standard_brand_name"),
            @Result(property = "firstCategory", column = "first_category"),
            @Result(property = "secondCategory", column = "second_category"),
            @Result(property = "thirdCategory", column = "third_category")
    })
    List<CommonGoodsAttr> selectGoodsAttrByBarcodeList(@Param("barcodes") List<String> barcodes);

}
