package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 库存快照明细
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_inventory_snapshot_detail")
public class InventorySnapshotDetail extends BaseEntity {
    @NotNull(message = "库存快照ID不能为空")
    private Long snapshotId;
    @JsonProperty("gCode")
    @NotBlank(message = "商品编码不能为空")
    private String gCode;
    @NotBlank(message = "商品名称不能为空")
    private String productName;
    @NotBlank(message = "仓库编码不能为空")
    private String warehouseId;
    private String batchCode;
    private LocalDateTime warehouseTime;
    private LocalDateTime productionTime;
    private LocalDateTime expireTime;
    @NotNull(message = "库存数量不能为空")
    private Integer stockCount;
}
