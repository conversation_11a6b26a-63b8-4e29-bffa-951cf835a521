package com.dstp.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.mapper.GoodsMapper;
import com.dstp.model.Goods;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品服务
 * 提供商品信息查询和缓存管理
 */
@Service
@Slf4j
@RequiredArgsConstructor
@CacheConfig(cacheNames = "goodsCache", cacheManager = "goodsCacheManager")
public class GoodsService extends BaseCacheService<Goods, String> {

    private final GoodsMapper goodsMapper;
    private final CacheManager goodsCacheManager;

    /**
     * 根据企业ID和商品编号获取商品信息
     * 使用缓存提高查询性能
     *
     * @param entId   企业ID
     * @param goodsNo 商品编号
     * @return 商品信息
     */
    @Cacheable(key = "#entId + ':' + #goodsNo", condition = "#entId != null && #goodsNo != null", unless = "#result == null")
    public Goods getGoodsByGoodsNo(Long entId, String goodsNo) {
        if (entId == null || StringUtils.isBlank(goodsNo)) {
            return null;
        }
        log.debug("缓存未命中，从数据库查询商品信息: 企业ID={}, 商品编码={}", entId, goodsNo);
        return goodsMapper.selectOne(new QueryWrapper<Goods>()
                .select("ent_id", "ent_goods_no", "bar_code", "brand", "parent_brand", "price", "first_category", "second_category", "third_category")
                .eq("ent_id", entId)
                .eq("ent_goods_no", goodsNo));
    }


    /**
     * 批量查询商品信息
     * 根据企业ID+商品编号批量获取商品信息
     *
     * @param goodsList    企业ID+商品编号的列表
     * @return 商品信息Map，key为企业ID+商品编号，value为商品信息
     */
    public Map<String, Goods> getGoodsByGoodsNoList(List<Goods> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return new HashMap<>();
        }

        // 使用Map去重, 如果有重复，保留第一个
        Map<String, Goods> distinctGoodsMap = goodsList.stream()
                .filter(goods -> goods.getEntId() != null && StringUtils.isNotBlank(goods.getEntGoodsNo()))
                .collect(Collectors.toMap(Goods::getUniqueKeyString, goods -> goods, (existing, replacement) -> existing));

        // 提取唯一键列表
        List<String> uniqueKeys = new ArrayList<>(distinctGoodsMap.keySet());

        // 使用基类的批量查询方法
        return batchGet(uniqueKeys);
    }

    @Override
    protected CacheManager getCacheManager() {
        return goodsCacheManager;
    }

    @Override
    protected String getCacheName() {
        return "goodsCache";
    }

    @Override
    protected String getCacheStatsPrefix() {
        return "商品";
    }

    @Override
    protected List<Goods> batchQueryFromDb(List<String> keys) {
        if (keys.isEmpty()) {
            return new ArrayList<>();
        }

        // 创建查询对象列表
        List<Goods> queryGoods = keys.stream()
                .map(key -> {
                    String[] parts = key.split(":");
                    if (parts.length == 2) {
                        Goods goods = new Goods();
                        goods.setEntId(Long.parseLong(parts[0]));
                        goods.setEntGoodsNo(parts[1]);
                        return goods;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return goodsMapper.selectByUniqueKeys(queryGoods);
    }

    @Override
    protected String getKey(Goods obj) {
        return obj.getUniqueKeyString();
    }
}
