2025-05-23 00:03:12.207 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 00:08:12.218 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 00:13:12.209 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 00:13:12.209 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 00:13:12.209 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 00:13:12.211 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 2, 命中率: 0.00%, 命中数: 0, 未命中数: 2, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 00:13:12.211 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 00:18:12.209 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 00:23:12.213 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 00:28:12.211 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 00:33:12.212 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 00:38:12.216 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 00:43:12.211 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 00:48:12.218 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 00:53:12.217 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 00:58:12.207 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 01:03:12.215 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 01:08:12.206 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 01:13:12.208 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 01:13:12.208 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 01:13:12.209 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 01:13:12.209 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 2, 命中率: 0.00%, 命中数: 0, 未命中数: 2, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 01:13:12.209 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 01:18:12.210 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 01:23:12.214 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 01:28:12.209 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 01:33:12.209 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 01:38:12.206 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 01:43:12.214 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 01:48:12.215 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 01:53:12.212 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 01:58:12.205 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 02:03:12.217 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 02:08:12.212 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 02:13:12.214 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 02:13:12.215 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 02:13:12.215 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 02:13:12.215 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 2, 命中率: 0.00%, 命中数: 0, 未命中数: 2, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 02:13:12.215 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 02:18:12.213 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 02:23:12.209 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 02:28:12.211 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 02:33:12.206 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 02:38:12.209 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 02:43:12.214 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 02:48:12.206 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 02:53:12.205 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 02:58:12.218 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 03:03:12.213 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 03:08:12.214 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 03:13:12.216 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 03:13:12.216 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 03:13:12.217 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 03:13:12.217 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 2, 命中率: 0.00%, 命中数: 0, 未命中数: 2, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 03:13:12.217 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 03:18:12.213 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 03:23:12.210 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 03:28:12.219 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 03:33:12.212 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 03:38:12.213 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 03:43:12.212 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 03:48:12.216 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 03:53:12.212 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 03:58:12.213 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 04:03:12.208 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 04:08:12.220 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 04:13:12.219 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 04:13:12.219 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 04:13:12.219 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 04:13:12.220 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 2, 命中率: 0.00%, 命中数: 0, 未命中数: 2, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 04:13:12.220 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 04:18:12.207 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 04:23:12.216 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 04:28:12.218 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 04:33:12.215 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 04:38:12.209 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 04:43:12.207 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 04:48:12.216 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 04:53:12.217 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 04:58:12.212 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 05:03:12.210 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 05:08:12.206 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 05:13:12.208 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 05:13:12.208 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 05:13:12.209 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 05:13:12.209 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 2, 命中率: 0.00%, 命中数: 0, 未命中数: 2, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 05:13:12.209 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 05:18:12.216 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 05:23:12.218 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 05:28:12.210 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 05:33:12.206 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 05:38:12.210 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 05:43:12.213 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 05:48:12.207 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 05:53:12.205 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 05:58:12.212 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 06:03:12.206 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 06:08:12.211 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 06:13:12.205 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 06:13:12.206 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 06:13:12.206 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 06:13:12.206 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 2, 命中率: 0.00%, 命中数: 0, 未命中数: 2, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 06:13:12.206 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 06:18:12.207 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 06:23:12.216 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 06:28:12.214 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 06:33:12.216 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 06:38:12.219 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 06:43:12.217 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 06:48:12.207 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 06:53:12.208 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 06:58:12.206 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 07:03:12.212 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 07:08:13.261 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 07:13:13.257 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 07:13:13.258 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 07:13:13.258 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 07:13:13.258 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 2, 命中率: 0.00%, 命中数: 0, 未命中数: 2, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 07:13:13.258 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 07:18:13.264 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 07:23:13.265 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 07:28:13.261 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 07:33:13.266 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 07:38:13.260 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 07:43:13.266 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 07:48:13.265 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 07:53:13.260 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 07:58:13.260 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 08:03:13.257 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 08:08:13.263 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 08:13:13.265 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 08:13:13.265 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 08:13:13.266 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 08:13:13.266 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 2, 命中率: 0.00%, 命中数: 0, 未命中数: 2, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 08:13:13.266 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 08:18:13.262 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 08:23:13.268 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 08:28:13.270 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 08:33:13.265 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 08:38:13.261 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 08:43:13.273 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 08:48:13.268 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 08:53:13.270 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 08:58:13.267 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 09:03:13.263 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 09:08:13.267 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 09:13:13.264 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 09:13:13.265 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 09:13:13.265 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 09:13:13.265 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 2, 命中率: 0.00%, 命中数: 0, 未命中数: 2, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 09:13:13.265 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 09:18:13.267 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 09:23:13.262 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 09:23:22.586 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Member dxc_sync_gel-3-6aa2ed64-b1fb-40cb-82b1-62fbd2253325 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 09:23:22.586 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Member dxc_sync_gel-1-304996fe-d76f-40b1-b93d-5b032f21fedc sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 09:23:22.586 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Member dxc_sync_gel-2-ebad634d-cb9c-4fd7-9b14-0b111025b96f sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 09:23:22.586 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions dxc_newly_tech_transport_info-0
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [dxc_newly_tech_transport_info-0]
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Member dxc_sync_gel-0-2c0640e8-2ad7-4cfc-99a8-cc5a9a83324e sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 09:23:22.620 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.620 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.620 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.620 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.620 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.620 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.620 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.620 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 09:23:22.651 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 09:23:22.651 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 09:23:22.651 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 09:23:22.651 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 09:23:22.651 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 09:23:22.651 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 09:23:22.651 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 09:23:22.651 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 09:23:22.658 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-3 unregistered
2025-05-23 09:23:22.658 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-1 unregistered
2025-05-23 09:23:22.659 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 09:23:22.659 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 09:23:22.674 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 09:23:22.674 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 09:23:22.674 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 09:23:22.674 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 09:23:22.677 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-2 unregistered
2025-05-23 09:23:22.677 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 09:23:22.846 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 09:23:22.846 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 09:23:22.846 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 09:23:22.846 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 09:23:22.849 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-0 unregistered
2025-05-23 09:23:22.849 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 09:23:22.875 INFO  SpringApplicationShutdownHook [org.springframework.boot.web.embedded.tomcat.GracefulShutdown:?]-Commencing graceful shutdown. Waiting for active requests to complete
2025-05-23 09:23:23.576 INFO  tomcat-shutdown [org.springframework.boot.web.embedded.tomcat.GracefulShutdown:?]-Graceful shutdown complete
2025-05-23 09:23:23.611 INFO  SpringApplicationShutdownHook [com.dstp.consumer.BatchMessageProcessor:?]-批处理器已关闭
2025-05-23 09:23:23.614 INFO  SpringApplicationShutdownHook [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Shutdown initiated...
2025-05-23 09:23:23.859 INFO  SpringApplicationShutdownHook [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Shutdown completed.
2025-05-23 09:51:52.226 INFO  main [com.dstp.Application:?]-Starting Application using Java ******** with PID 36640 (D:\workspace\dstp\project\dxc_sync_gel\target\classes started by zhenxing.chen in D:\workspace\dstp\project\dxc_sync_gel)
2025-05-23 09:51:52.227 INFO  main [com.dstp.Application:?]-The following 1 profile is active: "fat"
2025-05-23 09:51:56.742 INFO  main [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:?]-Tomcat initialized with port 8080 (http)
2025-05-23 09:51:56.755 INFO  main [org.apache.catalina.core.StandardService:?]-Starting service [Tomcat]
2025-05-23 09:51:56.755 INFO  main [org.apache.catalina.core.StandardEngine:?]-Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-05-23 09:51:56.886 INFO  main [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:?]-Initializing Spring embedded WebApplicationContext
2025-05-23 09:51:56.886 INFO  main [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:?]-Root WebApplicationContext: initialization completed in 4565 ms
2025-05-23 09:51:59.645 INFO  main [com.dstp.consumer.BatchMessageProcessor:?]-批处理器初始化完成，最大批量大小: 500, 处理间隔: 10秒, 线程池大小: 4
2025-05-23 09:52:00.811 INFO  main [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:?]-Exposing 5 endpoints beneath base path '/actuator'
2025-05-23 09:52:00.982 INFO  main [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:?]-Tomcat started on port 8080 (http) with context path '/'
2025-05-23 09:52:01.040 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-0
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 09:52:01.108 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 09:52:01.154 INFO  main [org.apache.kafka.common.security.authenticator.AbstractLogin:?]-Successfully logged in.
2025-05-23 09:52:01.229 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 09:52:01.231 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 09:52:01.231 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 09:52:01.231 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747965121229
2025-05-23 09:52:01.234 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Subscribed to topic(s): dxc_newly_tech_transport_info
2025-05-23 09:52:01.240 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-1
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 09:52:01.240 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 09:52:01.249 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 09:52:01.249 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 09:52:01.249 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 09:52:01.249 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747965121249
2025-05-23 09:52:01.249 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Subscribed to topic(s): dxc_newly_tech_transport_info
2025-05-23 09:52:01.251 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-2
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 09:52:01.251 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 09:52:01.259 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 09:52:01.259 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 09:52:01.259 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 09:52:01.259 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747965121259
2025-05-23 09:52:01.260 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Subscribed to topic(s): dxc_newly_tech_transport_info
2025-05-23 09:52:01.261 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-3
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 09:52:01.261 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 09:52:01.268 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 09:52:01.268 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 09:52:01.268 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 09:52:01.268 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747965121268
2025-05-23 09:52:01.268 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Subscribed to topic(s): dxc_newly_tech_transport_info
2025-05-23 09:52:01.287 INFO  main [com.dstp.Application:?]-Started Application in 9.874 seconds (process running for 11.059)
2025-05-23 09:52:01.305 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 09:52:01.314 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 09:52:01.319 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 09:52:01.322 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 09:52:01.325 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 0, 失败数: 0, 重试数: 0
2025-05-23 09:52:01.788 INFO  RMI TCP Connection(3)-********* [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:?]-Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-23 09:52:01.788 INFO  RMI TCP Connection(3)-********* [org.springframework.web.servlet.DispatcherServlet:?]-Initializing Servlet 'dispatcherServlet'
2025-05-23 09:52:01.792 INFO  RMI TCP Connection(3)-********* [org.springframework.web.servlet.DispatcherServlet:?]-Completed initialization in 3 ms
2025-05-23 09:52:01.807 INFO  RMI TCP Connection(2)-********* [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Starting...
2025-05-23 09:52:02.345 INFO  RMI TCP Connection(2)-********* [com.zaxxer.hikari.pool.HikariPool:?]-HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@b043c2e
2025-05-23 09:52:02.352 INFO  RMI TCP Connection(2)-********* [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Start completed.
2025-05-23 09:52:11.056 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 9520 ms.
2025-05-23 09:52:11.056 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 9751 ms.
2025-05-23 09:52:11.056 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node -1 due to socket connection setup timeout. The timeout value is 9193 ms.
2025-05-23 09:52:11.057 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 9528 ms.
2025-05-23 09:52:11.057 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -2 rack: null) disconnected
2025-05-23 09:52:11.057 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -2 rack: null) disconnected
2025-05-23 09:52:11.057 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -3 rack: null) disconnected
2025-05-23 09:52:11.058 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Bootstrap broker ***********:9094 (id: -1 rack: null) disconnected
2025-05-23 09:52:20.517 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node -1 due to socket connection setup timeout. The timeout value is 8834 ms.
2025-05-23 09:52:20.517 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Bootstrap broker ***********:9094 (id: -1 rack: null) disconnected
2025-05-23 09:52:20.517 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node -1 due to socket connection setup timeout. The timeout value is 9213 ms.
2025-05-23 09:52:20.517 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Bootstrap broker ***********:9094 (id: -1 rack: null) disconnected
2025-05-23 09:52:30.137 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 09:52:30.137 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 09:52:30.138 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 09:52:30.138 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 09:52:39.533 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 09:52:39.533 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 09:52:39.533 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 09:52:39.534 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 09:52:39.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:52:39.587 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:52:39.592 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 9829 ms.
2025-05-23 09:52:39.592 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 11446 ms.
2025-05-23 09:52:48.986 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:52:48.986 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:52:48.988 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 9237 ms.
2025-05-23 09:52:48.988 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 17299 ms.
2025-05-23 09:52:48.988 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 09:52:48.988 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 09:52:49.049 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 11402 ms.
2025-05-23 09:52:49.049 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 11572 ms.
2025-05-23 09:52:49.049 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 09:52:49.049 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 09:52:49.172 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 09:52:49.172 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 09:52:58.421 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 11330 ms.
2025-05-23 09:52:58.421 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node 2 due to socket connection setup timeout. The timeout value is 8936 ms.
2025-05-23 09:52:58.421 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node 2 due to socket connection setup timeout. The timeout value is 9217 ms.
2025-05-23 09:52:58.421 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 09:52:58.623 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 09:52:58.623 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 09:52:58.623 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:52:58.623 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:52:58.747 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-0-10ab5eab-fe15-4ead-a194-5dd72aef9241
2025-05-23 09:52:58.747 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-3-d0c2b90c-73ca-44e6-8096-dee4a0e8b3ff
2025-05-23 09:52:58.747 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:52:58.747 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:52:58.773 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=49, memberId='dxc_sync_gel-0-10ab5eab-fe15-4ead-a194-5dd72aef9241', protocol='range'}
2025-05-23 09:52:58.773 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=49, memberId='dxc_sync_gel-3-d0c2b90c-73ca-44e6-8096-dee4a0e8b3ff', protocol='range'}
2025-05-23 09:52:58.780 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Finished assignment for group at generation 49: {dxc_sync_gel-3-d0c2b90c-73ca-44e6-8096-dee4a0e8b3ff=Assignment(partitions=[]), dxc_sync_gel-0-10ab5eab-fe15-4ead-a194-5dd72aef9241=Assignment(partitions=[dxc_newly_tech_transport_info-0])}
2025-05-23 09:52:58.810 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=49, memberId='dxc_sync_gel-3-d0c2b90c-73ca-44e6-8096-dee4a0e8b3ff', protocol='range'}
2025-05-23 09:52:58.810 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=49, memberId='dxc_sync_gel-0-10ab5eab-fe15-4ead-a194-5dd72aef9241', protocol='range'}
2025-05-23 09:52:58.810 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 09:52:58.810 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[dxc_newly_tech_transport_info-0])
2025-05-23 09:52:58.811 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 09:52:58.812 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 09:52:58.813 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Adding newly assigned partitions: dxc_newly_tech_transport_info-0
2025-05-23 09:52:58.859 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition dxc_newly_tech_transport_info-0 to the committed offset FetchPosition{offset=112, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[************:9094 (id: 2 rack: null)], epoch=0}}
2025-05-23 09:52:58.859 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [dxc_newly_tech_transport_info-0]
2025-05-23 09:53:07.999 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 09:53:08.001 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 09:53:08.308 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node 2 due to socket connection setup timeout. The timeout value is 8360 ms.
2025-05-23 09:53:08.309 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.FetchSessionHandler:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Error sending fetch request (sessionId=INVALID, epoch=INITIAL) to node 2:
org.apache.kafka.common.errors.DisconnectException: null
2025-05-23 09:53:17.450 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:53:17.451 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 09:53:17.451 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:53:17.576 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-2-a2087597-4722-4083-b95f-de05a2872096
2025-05-23 09:53:17.576 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-1-87a9955b-da99-4622-880d-44d00ed0de2d
2025-05-23 09:53:17.576 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:53:17.576 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:53:17.930 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 09:53:17.931 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions dxc_newly_tech_transport_info-0
2025-05-23 09:53:17.931 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [dxc_newly_tech_transport_info-0]
2025-05-23 09:53:17.932 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:53:19.837 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 09:53:19.837 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Revoke previously assigned partitions 
2025-05-23 09:53:19.837 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: []
2025-05-23 09:53:19.837 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 09:53:19.863 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=50, memberId='dxc_sync_gel-2-a2087597-4722-4083-b95f-de05a2872096', protocol='range'}
2025-05-23 09:53:19.863 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=50, memberId='dxc_sync_gel-0-10ab5eab-fe15-4ead-a194-5dd72aef9241', protocol='range'}
2025-05-23 09:53:19.863 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=50, memberId='dxc_sync_gel-3-d0c2b90c-73ca-44e6-8096-dee4a0e8b3ff', protocol='range'}
2025-05-23 09:53:19.863 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=50, memberId='dxc_sync_gel-1-87a9955b-da99-4622-880d-44d00ed0de2d', protocol='range'}
2025-05-23 09:53:19.863 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Finished assignment for group at generation 50: {dxc_sync_gel-2-a2087597-4722-4083-b95f-de05a2872096=Assignment(partitions=[]), dxc_sync_gel-3-d0c2b90c-73ca-44e6-8096-dee4a0e8b3ff=Assignment(partitions=[]), dxc_sync_gel-0-10ab5eab-fe15-4ead-a194-5dd72aef9241=Assignment(partitions=[dxc_newly_tech_transport_info-0]), dxc_sync_gel-1-87a9955b-da99-4622-880d-44d00ed0de2d=Assignment(partitions=[])}
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=50, memberId='dxc_sync_gel-3-d0c2b90c-73ca-44e6-8096-dee4a0e8b3ff', protocol='range'}
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=50, memberId='dxc_sync_gel-1-87a9955b-da99-4622-880d-44d00ed0de2d', protocol='range'}
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=50, memberId='dxc_sync_gel-2-a2087597-4722-4083-b95f-de05a2872096', protocol='range'}
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=50, memberId='dxc_sync_gel-0-10ab5eab-fe15-4ead-a194-5dd72aef9241', protocol='range'}
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[dxc_newly_tech_transport_info-0])
2025-05-23 09:53:19.894 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Adding newly assigned partitions: dxc_newly_tech_transport_info-0
2025-05-23 09:53:19.920 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition dxc_newly_tech_transport_info-0 to the committed offset FetchPosition{offset=112, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[************:9094 (id: 2 rack: null)], epoch=0}}
2025-05-23 09:53:19.921 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [dxc_newly_tech_transport_info-0]
2025-05-23 09:53:24.709 INFO  batch-processor-1 [com.dstp.consumer.BatchMessageProcessor:?]-处理Topic: dxc_newly_tech_transport_info, 消息数量: 1
2025-05-23 09:57:01.287 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 09:57:30.569 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Node -1 disconnected.
2025-05-23 09:57:30.569 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Node -3 disconnected.
2025-05-23 09:57:39.780 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Node -2 disconnected.
2025-05-23 09:57:39.780 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Node -3 disconnected.
2025-05-23 09:58:08.452 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Node -3 disconnected.
2025-05-23 10:02:01.292 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 10:02:18.671 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions dxc_newly_tech_transport_info-0
2025-05-23 10:02:18.671 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Member dxc_sync_gel-3-d0c2b90c-73ca-44e6-8096-dee4a0e8b3ff sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:02:18.671 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [dxc_newly_tech_transport_info-0]
2025-05-23 10:02:18.671 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Member dxc_sync_gel-0-10ab5eab-fe15-4ead-a194-5dd72aef9241 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:02:18.672 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Member dxc_sync_gel-2-a2087597-4722-4083-b95f-de05a2872096 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:02:18.672 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.672 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.672 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.672 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.672 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:02:18.672 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:02:18.672 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.672 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.672 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:02:18.672 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Member dxc_sync_gel-1-87a9955b-da99-4622-880d-44d00ed0de2d sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:02:18.673 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.673 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.673 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:02:18.681 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.681 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.681 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.681 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.681 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.681 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.681 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.681 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:02:18.709 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:02:18.709 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:02:18.709 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:02:18.709 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:02:18.709 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:02:18.709 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:02:18.709 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:02:18.709 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:02:18.709 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:02:18.709 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:02:18.709 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:02:18.709 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:02:18.720 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-1 unregistered
2025-05-23 10:02:18.720 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-2 unregistered
2025-05-23 10:02:18.720 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:02:18.720 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-3 unregistered
2025-05-23 10:02:18.720 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:02:18.720 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:02:18.780 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:02:18.780 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:02:18.780 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:02:18.780 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:02:18.782 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-0 unregistered
2025-05-23 10:02:18.782 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:02:18.783 INFO  SpringApplicationShutdownHook [org.springframework.boot.web.embedded.tomcat.GracefulShutdown:?]-Commencing graceful shutdown. Waiting for active requests to complete
2025-05-23 10:02:19.259 INFO  tomcat-shutdown [org.springframework.boot.web.embedded.tomcat.GracefulShutdown:?]-Graceful shutdown complete
2025-05-23 10:02:19.277 INFO  SpringApplicationShutdownHook [com.dstp.consumer.BatchMessageProcessor:?]-批处理器已关闭
2025-05-23 10:02:19.279 INFO  SpringApplicationShutdownHook [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Shutdown initiated...
2025-05-23 10:02:19.532 INFO  SpringApplicationShutdownHook [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Shutdown completed.
2025-05-23 10:02:23.988 INFO  main [com.dstp.Application:?]-Starting Application using Java ******** with PID 25788 (D:\workspace\dstp\project\dxc_sync_gel\target\classes started by zhenxing.chen in D:\workspace\dstp\project\dxc_sync_gel)
2025-05-23 10:02:23.989 INFO  main [com.dstp.Application:?]-The following 1 profile is active: "fat"
2025-05-23 10:02:26.654 INFO  main [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:?]-Tomcat initialized with port 8080 (http)
2025-05-23 10:02:26.673 INFO  main [org.apache.catalina.core.StandardService:?]-Starting service [Tomcat]
2025-05-23 10:02:26.673 INFO  main [org.apache.catalina.core.StandardEngine:?]-Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-05-23 10:02:26.737 INFO  main [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:?]-Initializing Spring embedded WebApplicationContext
2025-05-23 10:02:26.737 INFO  main [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:?]-Root WebApplicationContext: initialization completed in 2704 ms
2025-05-23 10:02:29.114 INFO  main [com.dstp.consumer.BatchMessageProcessor:?]-批处理器初始化完成，最大批量大小: 500, 处理间隔: 10秒, 线程池大小: 4
2025-05-23 10:02:30.046 INFO  main [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:?]-Exposing 5 endpoints beneath base path '/actuator'
2025-05-23 10:02:30.189 INFO  main [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:?]-Tomcat started on port 8080 (http) with context path '/'
2025-05-23 10:02:30.224 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-0
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:02:30.264 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:02:30.320 INFO  main [org.apache.kafka.common.security.authenticator.AbstractLogin:?]-Successfully logged in.
2025-05-23 10:02:30.373 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:02:30.374 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:02:30.374 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:02:30.374 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747965750373
2025-05-23 10:02:30.376 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Subscribed to topic(s): dxc_newly_tech_transport_info
2025-05-23 10:02:30.384 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-1
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:02:30.384 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:02:30.392 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:02:30.392 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:02:30.392 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:02:30.392 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747965750392
2025-05-23 10:02:30.392 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Subscribed to topic(s): dxc_newly_tech_transport_info
2025-05-23 10:02:30.394 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-2
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:02:30.394 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:02:30.401 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:02:30.401 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:02:30.401 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:02:30.401 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747965750401
2025-05-23 10:02:30.401 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Subscribed to topic(s): dxc_newly_tech_transport_info
2025-05-23 10:02:30.403 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-3
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:02:30.403 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:02:30.408 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:02:30.408 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:02:30.408 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:02:30.408 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747965750408
2025-05-23 10:02:30.409 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Subscribed to topic(s): dxc_newly_tech_transport_info
2025-05-23 10:02:30.423 INFO  main [com.dstp.Application:?]-Started Application in 7.007 seconds (process running for 7.59)
2025-05-23 10:02:30.441 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:02:30.445 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:02:30.446 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:02:30.449 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:02:30.450 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 0, 失败数: 0, 重试数: 0
2025-05-23 10:02:30.754 INFO  RMI TCP Connection(5)-********* [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:?]-Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-23 10:02:30.754 INFO  RMI TCP Connection(5)-********* [org.springframework.web.servlet.DispatcherServlet:?]-Initializing Servlet 'dispatcherServlet'
2025-05-23 10:02:30.757 INFO  RMI TCP Connection(6)-********* [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Starting...
2025-05-23 10:02:30.758 INFO  RMI TCP Connection(5)-********* [org.springframework.web.servlet.DispatcherServlet:?]-Completed initialization in 4 ms
2025-05-23 10:02:31.196 INFO  RMI TCP Connection(6)-********* [com.zaxxer.hikari.pool.HikariPool:?]-HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6d75ee3c
2025-05-23 10:02:31.204 INFO  RMI TCP Connection(6)-********* [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Start completed.
2025-05-23 10:02:40.282 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 9448 ms.
2025-05-23 10:02:40.282 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 8491 ms.
2025-05-23 10:02:40.283 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -3 rack: null) disconnected
2025-05-23 10:02:40.283 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -2 rack: null) disconnected
2025-05-23 10:02:49.714 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node -1 due to socket connection setup timeout. The timeout value is 8933 ms.
2025-05-23 10:02:49.714 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Bootstrap broker ***********:9094 (id: -1 rack: null) disconnected
2025-05-23 10:02:49.881 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:02:49.881 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:02:49.882 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:02:49.882 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:02:59.254 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 9138 ms.
2025-05-23 10:02:59.254 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -3 rack: null) disconnected
2025-05-23 10:02:59.333 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:02:59.333 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:02:59.338 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node -1 due to socket connection setup timeout. The timeout value is 11804 ms.
2025-05-23 10:02:59.338 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 11675 ms.
2025-05-23 10:03:08.698 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:03:08.721 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:03:08.721 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:03:08.778 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 10662 ms.
2025-05-23 10:03:08.778 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 10021 ms.
2025-05-23 10:03:08.778 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 10:03:08.778 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 10:03:08.901 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:03:18.145 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node 1 due to socket connection setup timeout. The timeout value is 9328 ms.
2025-05-23 10:03:18.145 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 18626 ms.
2025-05-23 10:03:18.145 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:03:18.159 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:03:18.285 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8
2025-05-23 10:03:18.285 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:03:18.311 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=52, memberId='dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8', protocol='range'}
2025-05-23 10:03:18.324 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Finished assignment for group at generation 52: {dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8=Assignment(partitions=[dxc_newly_tech_transport_info-0])}
2025-05-23 10:03:18.346 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 10:03:18.346 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:03:18.353 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:03:18.356 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=52, memberId='dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8', protocol='range'}
2025-05-23 10:03:18.356 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[dxc_newly_tech_transport_info-0])
2025-05-23 10:03:18.359 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Adding newly assigned partitions: dxc_newly_tech_transport_info-0
2025-05-23 10:03:18.392 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition dxc_newly_tech_transport_info-0 to the committed offset FetchPosition{offset=112, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[************:9094 (id: 2 rack: null)], epoch=0}}
2025-05-23 10:03:18.393 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [dxc_newly_tech_transport_info-0]
2025-05-23 10:03:18.470 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-0-cd519471-ac92-4860-8789-f47e9e3883b4
2025-05-23 10:03:18.470 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:03:27.594 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:03:27.784 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node 0 due to socket connection setup timeout. The timeout value is 9922 ms.
2025-05-23 10:03:27.784 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 10:03:27.784 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:03:27.872 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:03:27.873 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Revoke previously assigned partitions dxc_newly_tech_transport_info-0
2025-05-23 10:03:27.873 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [dxc_newly_tech_transport_info-0]
2025-05-23 10:03:27.873 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:03:27.899 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=53, memberId='dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8', protocol='range'}
2025-05-23 10:03:27.899 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=53, memberId='dxc_sync_gel-0-cd519471-ac92-4860-8789-f47e9e3883b4', protocol='range'}
2025-05-23 10:03:27.899 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Finished assignment for group at generation 53: {dxc_sync_gel-0-cd519471-ac92-4860-8789-f47e9e3883b4=Assignment(partitions=[dxc_newly_tech_transport_info-0]), dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8=Assignment(partitions=[])}
2025-05-23 10:03:27.908 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-2-579906ac-7dc9-4ddb-8e00-16d21bfe37e7
2025-05-23 10:03:27.908 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:03:27.927 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=53, memberId='dxc_sync_gel-0-cd519471-ac92-4860-8789-f47e9e3883b4', protocol='range'}
2025-05-23 10:03:27.927 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=53, memberId='dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8', protocol='range'}
2025-05-23 10:03:27.927 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:03:27.927 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[dxc_newly_tech_transport_info-0])
2025-05-23 10:03:27.927 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:03:27.927 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Adding newly assigned partitions: dxc_newly_tech_transport_info-0
2025-05-23 10:03:27.928 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:03:27.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition dxc_newly_tech_transport_info-0 to the committed offset FetchPosition{offset=112, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[************:9094 (id: 2 rack: null)], epoch=0}}
2025-05-23 10:03:27.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [dxc_newly_tech_transport_info-0]
2025-05-23 10:03:30.932 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:03:30.932 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Revoke previously assigned partitions 
2025-05-23 10:03:30.933 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: []
2025-05-23 10:03:30.933 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:03:37.034 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 9175 ms.
2025-05-23 10:03:37.034 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node 2 due to socket connection setup timeout. The timeout value is 8851 ms.
2025-05-23 10:03:37.034 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 10:03:37.034 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 10:03:37.393 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node 2 due to socket connection setup timeout. The timeout value is 8901 ms.
2025-05-23 10:03:37.393 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.FetchSessionHandler:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Error sending fetch request (sessionId=INVALID, epoch=INITIAL) to node 2:
org.apache.kafka.common.errors.DisconnectException: null
2025-05-23 10:03:37.428 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:03:37.428 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions dxc_newly_tech_transport_info-0
2025-05-23 10:03:37.428 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [dxc_newly_tech_transport_info-0]
2025-05-23 10:03:37.428 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:03:37.455 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=54, memberId='dxc_sync_gel-2-579906ac-7dc9-4ddb-8e00-16d21bfe37e7', protocol='range'}
2025-05-23 10:03:37.455 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=54, memberId='dxc_sync_gel-0-cd519471-ac92-4860-8789-f47e9e3883b4', protocol='range'}
2025-05-23 10:03:37.455 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=54, memberId='dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8', protocol='range'}
2025-05-23 10:03:37.455 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Finished assignment for group at generation 54: {dxc_sync_gel-0-cd519471-ac92-4860-8789-f47e9e3883b4=Assignment(partitions=[dxc_newly_tech_transport_info-0]), dxc_sync_gel-2-579906ac-7dc9-4ddb-8e00-16d21bfe37e7=Assignment(partitions=[]), dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8=Assignment(partitions=[])}
2025-05-23 10:03:37.483 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=54, memberId='dxc_sync_gel-0-cd519471-ac92-4860-8789-f47e9e3883b4', protocol='range'}
2025-05-23 10:03:37.483 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=54, memberId='dxc_sync_gel-2-579906ac-7dc9-4ddb-8e00-16d21bfe37e7', protocol='range'}
2025-05-23 10:03:37.483 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[dxc_newly_tech_transport_info-0])
2025-05-23 10:03:37.483 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=54, memberId='dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8', protocol='range'}
2025-05-23 10:03:37.483 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Adding newly assigned partitions: dxc_newly_tech_transport_info-0
2025-05-23 10:03:37.483 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:03:37.483 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:03:37.484 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:03:37.484 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:03:37.484 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:03:37.484 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:03:37.508 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition dxc_newly_tech_transport_info-0 to the committed offset FetchPosition{offset=112, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[************:9094 (id: 2 rack: null)], epoch=0}}
2025-05-23 10:03:37.508 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [dxc_newly_tech_transport_info-0]
2025-05-23 10:03:46.481 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node 0 due to socket connection setup timeout. The timeout value is 8459 ms.
2025-05-23 10:03:54.192 INFO  batch-processor-1 [com.dstp.consumer.BatchMessageProcessor:?]-处理Topic: dxc_newly_tech_transport_info, 消息数量: 1
2025-05-23 10:04:05.489 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:04:14.924 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:04:15.046 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-3-db14ace7-48ed-4630-9a0e-34f809385ee0
2025-05-23 10:04:15.046 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:04:16.555 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:04:16.555 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Revoke previously assigned partitions 
2025-05-23 10:04:16.555 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: []
2025-05-23 10:04:16.555 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:04:16.570 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:04:16.570 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Revoke previously assigned partitions 
2025-05-23 10:04:16.570 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: []
2025-05-23 10:04:16.570 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:04:17.039 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:04:17.039 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions dxc_newly_tech_transport_info-0
2025-05-23 10:04:17.039 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [dxc_newly_tech_transport_info-0]
2025-05-23 10:04:17.039 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:04:17.063 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=55, memberId='dxc_sync_gel-0-cd519471-ac92-4860-8789-f47e9e3883b4', protocol='range'}
2025-05-23 10:04:17.063 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=55, memberId='dxc_sync_gel-2-579906ac-7dc9-4ddb-8e00-16d21bfe37e7', protocol='range'}
2025-05-23 10:04:17.063 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=55, memberId='dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8', protocol='range'}
2025-05-23 10:04:17.063 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=55, memberId='dxc_sync_gel-3-db14ace7-48ed-4630-9a0e-34f809385ee0', protocol='range'}
2025-05-23 10:04:17.063 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Finished assignment for group at generation 55: {dxc_sync_gel-0-cd519471-ac92-4860-8789-f47e9e3883b4=Assignment(partitions=[dxc_newly_tech_transport_info-0]), dxc_sync_gel-2-579906ac-7dc9-4ddb-8e00-16d21bfe37e7=Assignment(partitions=[]), dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8=Assignment(partitions=[]), dxc_sync_gel-3-db14ace7-48ed-4630-9a0e-34f809385ee0=Assignment(partitions=[])}
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=55, memberId='dxc_sync_gel-0-cd519471-ac92-4860-8789-f47e9e3883b4', protocol='range'}
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=55, memberId='dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8', protocol='range'}
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=55, memberId='dxc_sync_gel-3-db14ace7-48ed-4630-9a0e-34f809385ee0', protocol='range'}
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=55, memberId='dxc_sync_gel-2-579906ac-7dc9-4ddb-8e00-16d21bfe37e7', protocol='range'}
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[dxc_newly_tech_transport_info-0])
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Adding newly assigned partitions: dxc_newly_tech_transport_info-0
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:04:17.092 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:04:17.117 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition dxc_newly_tech_transport_info-0 to the committed offset FetchPosition{offset=113, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[************:9094 (id: 2 rack: null)], epoch=0}}
2025-05-23 10:04:17.117 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [dxc_newly_tech_transport_info-0]
2025-05-23 10:07:02.863 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Member dxc_sync_gel-2-579906ac-7dc9-4ddb-8e00-16d21bfe37e7 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:07:02.863 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Member dxc_sync_gel-1-a4f65f42-b042-459b-beb3-54cf55b1c1f8 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:07:02.864 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.864 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Member dxc_sync_gel-3-db14ace7-48ed-4630-9a0e-34f809385ee0 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:07:02.864 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.864 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.864 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.864 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:07:02.864 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:07:02.864 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.864 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.865 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:07:02.866 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.866 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.866 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.866 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.866 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions dxc_newly_tech_transport_info-0
2025-05-23 10:07:02.866 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [dxc_newly_tech_transport_info-0]
2025-05-23 10:07:02.867 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Member dxc_sync_gel-0-cd519471-ac92-4860-8789-f47e9e3883b4 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:07:02.867 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.867 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.867 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:07:02.867 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.867 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.868 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.868 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:07:02.895 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:07:02.895 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:07:02.895 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:07:02.895 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:07:02.895 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:07:02.895 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:07:02.895 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:07:02.895 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:07:02.901 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-2 unregistered
2025-05-23 10:07:02.902 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:07:02.902 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-3 unregistered
2025-05-23 10:07:02.902 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:07:02.918 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:07:02.919 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:07:02.919 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:07:02.919 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:07:02.921 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-1 unregistered
2025-05-23 10:07:02.921 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:07:03.200 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:07:03.200 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:07:03.200 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:07:03.200 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:07:03.203 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-0 unregistered
2025-05-23 10:07:03.203 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:07:03.204 INFO  SpringApplicationShutdownHook [org.springframework.boot.web.embedded.tomcat.GracefulShutdown:?]-Commencing graceful shutdown. Waiting for active requests to complete
2025-05-23 10:07:03.682 INFO  tomcat-shutdown [org.springframework.boot.web.embedded.tomcat.GracefulShutdown:?]-Graceful shutdown complete
2025-05-23 10:07:03.701 INFO  SpringApplicationShutdownHook [com.dstp.consumer.BatchMessageProcessor:?]-批处理器已关闭
2025-05-23 10:07:03.704 INFO  SpringApplicationShutdownHook [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Shutdown initiated...
2025-05-23 10:07:03.970 INFO  SpringApplicationShutdownHook [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Shutdown completed.
2025-05-23 10:07:08.346 INFO  main [com.dstp.Application:?]-Starting Application using Java ******** with PID 35488 (D:\workspace\dstp\project\dxc_sync_gel\target\classes started by zhenxing.chen in D:\workspace\dstp\project\dxc_sync_gel)
2025-05-23 10:07:08.348 INFO  main [com.dstp.Application:?]-The following 1 profile is active: "fat"
2025-05-23 10:07:11.096 INFO  main [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:?]-Tomcat initialized with port 8080 (http)
2025-05-23 10:07:11.114 INFO  main [org.apache.catalina.core.StandardService:?]-Starting service [Tomcat]
2025-05-23 10:07:11.114 INFO  main [org.apache.catalina.core.StandardEngine:?]-Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-05-23 10:07:11.176 INFO  main [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:?]-Initializing Spring embedded WebApplicationContext
2025-05-23 10:07:11.176 INFO  main [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:?]-Root WebApplicationContext: initialization completed in 2780 ms
2025-05-23 10:07:13.891 INFO  main [com.dstp.consumer.BatchMessageProcessor:?]-批处理器初始化完成，最大批量大小: 500, 处理间隔: 10秒, 线程池大小: 4
2025-05-23 10:07:14.934 INFO  main [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:?]-Exposing 5 endpoints beneath base path '/actuator'
2025-05-23 10:07:15.074 INFO  main [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:?]-Tomcat started on port 8080 (http) with context path '/'
2025-05-23 10:07:15.107 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-0
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:07:15.177 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:07:15.268 INFO  main [org.apache.kafka.common.security.authenticator.AbstractLogin:?]-Successfully logged in.
2025-05-23 10:07:15.370 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:07:15.371 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:07:15.371 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:07:15.371 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747966035370
2025-05-23 10:07:15.374 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Subscribed to topic(s): dxc_newly_tech_transport_info
2025-05-23 10:07:15.382 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-1
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:07:15.383 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:07:15.392 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:07:15.393 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:07:15.393 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:07:15.393 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747966035393
2025-05-23 10:07:15.393 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Subscribed to topic(s): dxc_newly_tech_transport_info
2025-05-23 10:07:15.396 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-2
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:07:15.396 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:07:15.405 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:07:15.405 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:07:15.405 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:07:15.405 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747966035405
2025-05-23 10:07:15.405 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Subscribed to topic(s): dxc_newly_tech_transport_info
2025-05-23 10:07:15.406 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-3
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:07:15.408 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:07:15.414 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:07:15.414 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:07:15.414 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:07:15.414 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747966035414
2025-05-23 10:07:15.415 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Subscribed to topic(s): dxc_newly_tech_transport_info
2025-05-23 10:07:15.435 INFO  main [com.dstp.Application:?]-Started Application in 7.666 seconds (process running for 8.267)
2025-05-23 10:07:15.450 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:07:15.464 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:07:15.467 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:07:15.469 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:07:15.471 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 0, 失败数: 0, 重试数: 0
2025-05-23 10:07:16.079 INFO  RMI TCP Connection(2)-********* [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:?]-Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-23 10:07:16.079 INFO  RMI TCP Connection(2)-********* [org.springframework.web.servlet.DispatcherServlet:?]-Initializing Servlet 'dispatcherServlet'
2025-05-23 10:07:16.080 INFO  RMI TCP Connection(3)-********* [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Starting...
2025-05-23 10:07:16.082 INFO  RMI TCP Connection(2)-********* [org.springframework.web.servlet.DispatcherServlet:?]-Completed initialization in 2 ms
2025-05-23 10:07:16.565 INFO  RMI TCP Connection(3)-********* [com.zaxxer.hikari.pool.HikariPool:?]-HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@e58a30f
2025-05-23 10:07:16.574 INFO  RMI TCP Connection(3)-********* [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Start completed.
2025-05-23 10:07:25.351 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 9011 ms.
2025-05-23 10:07:25.351 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 9755 ms.
2025-05-23 10:07:25.352 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -3 rack: null) disconnected
2025-05-23 10:07:25.353 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -3 rack: null) disconnected
2025-05-23 10:07:34.800 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node -1 due to socket connection setup timeout. The timeout value is 8641 ms.
2025-05-23 10:07:34.801 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Bootstrap broker ***********:9094 (id: -1 rack: null) disconnected
2025-05-23 10:07:34.938 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:07:34.938 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:07:34.938 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:07:34.938 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:07:44.370 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:07:44.370 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:07:44.392 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:07:44.392 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:07:44.397 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 9264 ms.
2025-05-23 10:07:44.397 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 8250 ms.
2025-05-23 10:07:44.397 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 11091 ms.
2025-05-23 10:07:44.397 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 10476 ms.
2025-05-23 10:07:44.397 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 10:07:44.397 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 10:07:44.397 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 10:07:44.397 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 10:07:53.820 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:07:53.820 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:07:53.826 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:07:53.827 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 11773 ms.
2025-05-23 10:07:53.962 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:07:53.963 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:08:03.269 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:08:03.270 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node 1 due to socket connection setup timeout. The timeout value is 8387 ms.
2025-05-23 10:08:03.270 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 10969 ms.
2025-05-23 10:08:03.271 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 10:08:03.394 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6
2025-05-23 10:08:03.395 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:08:03.410 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:08:03.410 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:08:03.420 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=57, memberId='dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6', protocol='range'}
2025-05-23 10:08:03.424 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Finished assignment for group at generation 57: {dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6=Assignment(partitions=[dxc_newly_tech_transport_info-0])}
2025-05-23 10:08:03.455 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=57, memberId='dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6', protocol='range'}
2025-05-23 10:08:03.455 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[dxc_newly_tech_transport_info-0])
2025-05-23 10:08:03.457 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: dxc_newly_tech_transport_info-0
2025-05-23 10:08:03.486 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition dxc_newly_tech_transport_info-0 to the committed offset FetchPosition{offset=112, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[************:9094 (id: 2 rack: null)], epoch=0}}
2025-05-23 10:08:03.486 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [dxc_newly_tech_transport_info-0]
2025-05-23 10:08:03.535 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-2-faee4b2c-d597-4e3d-8819-159e63c1073f
2025-05-23 10:08:03.535 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-1-abd1697b-7465-458a-be7d-6b008bb4c653
2025-05-23 10:08:03.535 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:08:03.535 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:08:12.838 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:08:12.975 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:08:12.976 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Revoke previously assigned partitions dxc_newly_tech_transport_info-0
2025-05-23 10:08:12.977 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [dxc_newly_tech_transport_info-0]
2025-05-23 10:08:12.977 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:08:13.002 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=58, memberId='dxc_sync_gel-1-abd1697b-7465-458a-be7d-6b008bb4c653', protocol='range'}
2025-05-23 10:08:13.002 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=58, memberId='dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6', protocol='range'}
2025-05-23 10:08:13.002 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=58, memberId='dxc_sync_gel-2-faee4b2c-d597-4e3d-8819-159e63c1073f', protocol='range'}
2025-05-23 10:08:13.002 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Finished assignment for group at generation 58: {dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6=Assignment(partitions=[]), dxc_sync_gel-1-abd1697b-7465-458a-be7d-6b008bb4c653=Assignment(partitions=[dxc_newly_tech_transport_info-0]), dxc_sync_gel-2-faee4b2c-d597-4e3d-8819-159e63c1073f=Assignment(partitions=[])}
2025-05-23 10:08:13.030 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=58, memberId='dxc_sync_gel-2-faee4b2c-d597-4e3d-8819-159e63c1073f', protocol='range'}
2025-05-23 10:08:13.030 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=58, memberId='dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6', protocol='range'}
2025-05-23 10:08:13.030 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=58, memberId='dxc_sync_gel-1-abd1697b-7465-458a-be7d-6b008bb4c653', protocol='range'}
2025-05-23 10:08:13.031 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:08:13.031 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:08:13.031 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:08:13.031 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:08:13.031 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[dxc_newly_tech_transport_info-0])
2025-05-23 10:08:13.031 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:08:13.031 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Adding newly assigned partitions: dxc_newly_tech_transport_info-0
2025-05-23 10:08:13.031 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:08:13.057 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition dxc_newly_tech_transport_info-0 to the committed offset FetchPosition{offset=112, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[************:9094 (id: 2 rack: null)], epoch=0}}
2025-05-23 10:08:13.058 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [dxc_newly_tech_transport_info-0]
2025-05-23 10:08:18.962 INFO  batch-processor-1 [com.dstp.consumer.BatchMessageProcessor:?]-处理Topic: dxc_newly_tech_transport_info, 消息数量: 1
2025-05-23 10:08:19.643 INFO  batch-processor-1 [com.dstp.consumer.BatchMessageProcessor:?]-Topic: dxc_newly_tech_transport_info 处理完成, 消息数: 1, 耗时: 681ms, 平均: 681ms/条
2025-05-23 10:08:22.283 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 10:08:22.283 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:08:22.413 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-0-c9782e1f-6f25-4ce4-b093-804c3abd9510
2025-05-23 10:08:22.413 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:08:25.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:08:25.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Revoke previously assigned partitions 
2025-05-23 10:08:25.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:08:25.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:08:25.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Revoke previously assigned partitions 
2025-05-23 10:08:25.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: []
2025-05-23 10:08:25.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Revoke previously assigned partitions dxc_newly_tech_transport_info-0
2025-05-23 10:08:25.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: []
2025-05-23 10:08:25.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:08:25.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [dxc_newly_tech_transport_info-0]
2025-05-23 10:08:25.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:08:25.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:08:25.094 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=59, memberId='dxc_sync_gel-1-abd1697b-7465-458a-be7d-6b008bb4c653', protocol='range'}
2025-05-23 10:08:25.094 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=59, memberId='dxc_sync_gel-2-faee4b2c-d597-4e3d-8819-159e63c1073f', protocol='range'}
2025-05-23 10:08:25.094 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=59, memberId='dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6', protocol='range'}
2025-05-23 10:08:25.094 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=59, memberId='dxc_sync_gel-0-c9782e1f-6f25-4ce4-b093-804c3abd9510', protocol='range'}
2025-05-23 10:08:25.094 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Finished assignment for group at generation 59: {dxc_sync_gel-0-c9782e1f-6f25-4ce4-b093-804c3abd9510=Assignment(partitions=[dxc_newly_tech_transport_info-0]), dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6=Assignment(partitions=[]), dxc_sync_gel-1-abd1697b-7465-458a-be7d-6b008bb4c653=Assignment(partitions=[]), dxc_sync_gel-2-faee4b2c-d597-4e3d-8819-159e63c1073f=Assignment(partitions=[])}
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=59, memberId='dxc_sync_gel-2-faee4b2c-d597-4e3d-8819-159e63c1073f', protocol='range'}
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=59, memberId='dxc_sync_gel-1-abd1697b-7465-458a-be7d-6b008bb4c653', protocol='range'}
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=59, memberId='dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6', protocol='range'}
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=59, memberId='dxc_sync_gel-0-c9782e1f-6f25-4ce4-b093-804c3abd9510', protocol='range'}
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[dxc_newly_tech_transport_info-0])
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Adding newly assigned partitions: dxc_newly_tech_transport_info-0
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:08:25.122 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:08:25.148 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition dxc_newly_tech_transport_info-0 to the committed offset FetchPosition{offset=113, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[************:9094 (id: 2 rack: null)], epoch=0}}
2025-05-23 10:08:25.148 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [dxc_newly_tech_transport_info-0]
2025-05-23 10:08:34.592 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node 2 due to socket connection setup timeout. The timeout value is 8743 ms.
2025-05-23 10:08:34.592 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.FetchSessionHandler:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Error sending fetch request (sessionId=INVALID, epoch=INITIAL) to node 2:
org.apache.kafka.common.errors.DisconnectException: null
2025-05-23 10:12:15.439 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 10:12:29.876 INFO  batch-processor-2 [com.dstp.consumer.BatchMessageProcessor:?]-处理Topic: dxc_newly_tech_transport_info, 消息数量: 1
2025-05-23 10:12:30.458 INFO  batch-processor-2 [com.dstp.consumer.BatchMessageProcessor:?]-Topic: dxc_newly_tech_transport_info 处理完成, 消息数: 1, 耗时: 582ms, 平均: 582ms/条
2025-05-23 10:12:35.772 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Node -1 disconnected.
2025-05-23 10:12:35.772 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Node -2 disconnected.
2025-05-23 10:12:44.987 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Node -1 disconnected.
2025-05-23 10:12:54.203 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Node -2 disconnected.
2025-05-23 10:13:04.443 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Node -3 disconnected.
2025-05-23 10:17:15.444 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 2, 失败数: 0, 重试数: 0
2025-05-23 10:17:44.370 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Node 1 disconnected.
2025-05-23 10:17:44.370 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Node 2 disconnected.
2025-05-23 10:17:44.371 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node -1 due to socket connection setup timeout. The timeout value is 9326 ms.
2025-05-23 10:17:53.808 INFO  kafka-coordinator-heartbeat-thread | dxc_sync_gel [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Node 0 disconnected.
2025-05-23 10:17:53.809 INFO  kafka-coordinator-heartbeat-thread | dxc_sync_gel [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node 1 due to socket connection setup timeout. The timeout value is 9770 ms.
2025-05-23 10:17:53.809 INFO  kafka-coordinator-heartbeat-thread | dxc_sync_gel [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node 0 due to socket connection setup timeout. The timeout value is 10668 ms.
2025-05-23 10:17:53.809 INFO  kafka-coordinator-heartbeat-thread | dxc_sync_gel [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 10259 ms.
2025-05-23 10:18:02.843 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:18:02.843 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions dxc_newly_tech_transport_info-0
2025-05-23 10:18:02.843 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [dxc_newly_tech_transport_info-0]
2025-05-23 10:18:02.843 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:18:03.239 INFO  kafka-coordinator-heartbeat-thread | dxc_sync_gel [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: session timed out without receiving a heartbeat response. isDisconnected: false. Rediscovery will be attempted.
2025-05-23 10:18:03.239 INFO  kafka-coordinator-heartbeat-thread | dxc_sync_gel [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: session timed out without receiving a heartbeat response. isDisconnected: false. Rediscovery will be attempted.
2025-05-23 10:18:03.239 INFO  kafka-coordinator-heartbeat-thread | dxc_sync_gel [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Requesting disconnect from last known coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:18:03.239 INFO  kafka-coordinator-heartbeat-thread | dxc_sync_gel [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Requesting disconnect from last known coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:18:03.239 INFO  kafka-coordinator-heartbeat-thread | dxc_sync_gel [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Client requested disconnect from node 2147483647
2025-05-23 10:18:03.239 INFO  kafka-coordinator-heartbeat-thread | dxc_sync_gel [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Client requested disconnect from node 2147483647
2025-05-23 10:18:03.239 INFO  kafka-coordinator-heartbeat-thread | dxc_sync_gel [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Cancelled in-flight HEARTBEAT request with correlation id 202 due to node 2147483647 being disconnected (elapsed time since creation: 9430ms, elapsed time since send: 9430ms, throttle time: 0ms, request timeout: 600000ms)
2025-05-23 10:18:03.239 INFO  kafka-coordinator-heartbeat-thread | dxc_sync_gel [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Cancelled in-flight HEARTBEAT request with correlation id 228 due to node 2147483647 being disconnected (elapsed time since creation: 9430ms, elapsed time since send: 9430ms, throttle time: 0ms, request timeout: 600000ms)
2025-05-23 10:18:03.264 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:18:03.264 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Revoke previously assigned partitions 
2025-05-23 10:18:03.264 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: []
2025-05-23 10:18:03.264 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:18:03.289 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=60, memberId='dxc_sync_gel-0-c9782e1f-6f25-4ce4-b093-804c3abd9510', protocol='range'}
2025-05-23 10:18:03.289 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=60, memberId='dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6', protocol='range'}
2025-05-23 10:18:03.290 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Finished assignment for group at generation 60: {dxc_sync_gel-0-c9782e1f-6f25-4ce4-b093-804c3abd9510=Assignment(partitions=[dxc_newly_tech_transport_info-0]), dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6=Assignment(partitions=[])}
2025-05-23 10:18:03.317 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=60, memberId='dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6', protocol='range'}
2025-05-23 10:18:03.317 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=60, memberId='dxc_sync_gel-0-c9782e1f-6f25-4ce4-b093-804c3abd9510', protocol='range'}
2025-05-23 10:18:03.317 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:18:03.317 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[dxc_newly_tech_transport_info-0])
2025-05-23 10:18:03.317 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:18:03.317 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Adding newly assigned partitions: dxc_newly_tech_transport_info-0
2025-05-23 10:18:03.317 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:18:03.343 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition dxc_newly_tech_transport_info-0 to the committed offset FetchPosition{offset=114, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[************:9094 (id: 2 rack: null)], epoch=0}}
2025-05-23 10:18:03.343 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [dxc_newly_tech_transport_info-0]
2025-05-23 10:18:12.795 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:18:12.796 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:18:22.229 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node 1 due to socket connection setup timeout. The timeout value is 9638 ms.
2025-05-23 10:18:22.229 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:18:22.245 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node 2 due to socket connection setup timeout. The timeout value is 11069 ms.
2025-05-23 10:18:22.246 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:18:22.354 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Attempt to heartbeat with Generation{generationId=59, memberId='dxc_sync_gel-2-faee4b2c-d597-4e3d-8819-159e63c1073f', protocol='range'} and group instance id Optional.empty failed due to UNKNOWN_MEMBER_ID, resetting generation
2025-05-23 10:18:22.354 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting generation and member id due to: encountered UNKNOWN_MEMBER_ID from HEARTBEAT response
2025-05-23 10:18:22.354 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: encountered UNKNOWN_MEMBER_ID from HEARTBEAT response
2025-05-23 10:18:22.354 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:18:22.374 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Attempt to heartbeat with Generation{generationId=59, memberId='dxc_sync_gel-1-abd1697b-7465-458a-be7d-6b008bb4c653', protocol='range'} and group instance id Optional.empty failed due to UNKNOWN_MEMBER_ID, resetting generation
2025-05-23 10:18:22.374 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Resetting generation and member id due to: encountered UNKNOWN_MEMBER_ID from HEARTBEAT response
2025-05-23 10:18:22.374 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: encountered UNKNOWN_MEMBER_ID from HEARTBEAT response
2025-05-23 10:18:22.374 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:18:22.386 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-2-c4a9c908-1e3c-427a-89d1-77a80dcff266
2025-05-23 10:18:22.386 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:18:22.400 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-1-46fb7f24-2105-4dbe-835b-4851d6b3ff95
2025-05-23 10:18:22.400 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:18:24.346 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:18:24.346 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions dxc_newly_tech_transport_info-0
2025-05-23 10:18:24.346 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [dxc_newly_tech_transport_info-0]
2025-05-23 10:18:24.346 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:18:24.354 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:18:24.354 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Revoke previously assigned partitions 
2025-05-23 10:18:24.354 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: []
2025-05-23 10:18:24.354 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:18:24.379 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=61, memberId='dxc_sync_gel-2-c4a9c908-1e3c-427a-89d1-77a80dcff266', protocol='range'}
2025-05-23 10:18:24.379 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=61, memberId='dxc_sync_gel-0-c9782e1f-6f25-4ce4-b093-804c3abd9510', protocol='range'}
2025-05-23 10:18:24.379 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=61, memberId='dxc_sync_gel-1-46fb7f24-2105-4dbe-835b-4851d6b3ff95', protocol='range'}
2025-05-23 10:18:24.379 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=61, memberId='dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6', protocol='range'}
2025-05-23 10:18:24.380 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Finished assignment for group at generation 61: {dxc_sync_gel-1-46fb7f24-2105-4dbe-835b-4851d6b3ff95=Assignment(partitions=[]), dxc_sync_gel-0-c9782e1f-6f25-4ce4-b093-804c3abd9510=Assignment(partitions=[dxc_newly_tech_transport_info-0]), dxc_sync_gel-2-c4a9c908-1e3c-427a-89d1-77a80dcff266=Assignment(partitions=[]), dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6=Assignment(partitions=[])}
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=61, memberId='dxc_sync_gel-1-46fb7f24-2105-4dbe-835b-4851d6b3ff95', protocol='range'}
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=61, memberId='dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6', protocol='range'}
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=61, memberId='dxc_sync_gel-2-c4a9c908-1e3c-427a-89d1-77a80dcff266', protocol='range'}
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=61, memberId='dxc_sync_gel-0-c9782e1f-6f25-4ce4-b093-804c3abd9510', protocol='range'}
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[dxc_newly_tech_transport_info-0])
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Adding newly assigned partitions: dxc_newly_tech_transport_info-0
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:18:24.409 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:18:24.434 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition dxc_newly_tech_transport_info-0 to the committed offset FetchPosition{offset=114, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[************:9094 (id: 2 rack: null)], epoch=0}}
2025-05-23 10:18:24.434 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [dxc_newly_tech_transport_info-0]
2025-05-23 10:22:15.435 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 2, 失败数: 0, 重试数: 0
2025-05-23 10:22:54.262 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Node -1 disconnected.
2025-05-23 10:23:04.501 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Node -2 disconnected.
2025-05-23 10:26:50.176 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Member dxc_sync_gel-2-c4a9c908-1e3c-427a-89d1-77a80dcff266 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:26:50.176 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions dxc_newly_tech_transport_info-0
2025-05-23 10:26:50.176 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [dxc_newly_tech_transport_info-0]
2025-05-23 10:26:50.176 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Member dxc_sync_gel-0-c9782e1f-6f25-4ce4-b093-804c3abd9510 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:26:50.176 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Member dxc_sync_gel-3-59f49ae1-ed42-4f80-9b1c-bf4780d673d6 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:26:50.177 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.177 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.177 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.177 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.177 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.177 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.177 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:26:50.177 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:26:50.177 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:26:50.178 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Member dxc_sync_gel-1-46fb7f24-2105-4dbe-835b-4851d6b3ff95 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:26:50.178 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.178 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.178 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:26:50.192 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.192 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.192 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.192 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.192 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.192 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.192 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.192 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:26:50.241 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:26:50.241 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:26:50.241 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:26:50.241 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:26:50.244 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-2 unregistered
2025-05-23 10:26:50.245 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:26:50.277 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:26:50.277 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:26:50.277 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:26:50.277 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:26:50.281 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-3 unregistered
2025-05-23 10:26:50.281 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:26:50.442 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:26:50.442 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:26:50.442 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:26:50.442 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:26:50.444 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-0 unregistered
2025-05-23 10:26:50.444 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:26:59.804 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:26:59.804 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:26:59.804 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:26:59.804 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:26:59.806 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-1 unregistered
2025-05-23 10:26:59.806 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:26:59.807 INFO  SpringApplicationShutdownHook [org.springframework.boot.web.embedded.tomcat.GracefulShutdown:?]-Commencing graceful shutdown. Waiting for active requests to complete
2025-05-23 10:27:00.279 INFO  tomcat-shutdown [org.springframework.boot.web.embedded.tomcat.GracefulShutdown:?]-Graceful shutdown complete
2025-05-23 10:27:00.324 INFO  SpringApplicationShutdownHook [com.dstp.consumer.BatchMessageProcessor:?]-批处理器已关闭
2025-05-23 10:27:00.328 INFO  SpringApplicationShutdownHook [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Shutdown initiated...
2025-05-23 10:27:00.594 INFO  SpringApplicationShutdownHook [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Shutdown completed.
2025-05-23 10:42:14.848 INFO  main [com.dstp.Application:?]-Starting Application using Java ******** with PID 26972 (D:\workspace\dstp\project\dxc_sync_gel\target\classes started by zhenxing.chen in D:\workspace\dstp\project\dxc_sync_gel)
2025-05-23 10:42:14.849 INFO  main [com.dstp.Application:?]-The following 1 profile is active: "fat"
2025-05-23 10:42:17.918 INFO  main [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:?]-Tomcat initialized with port 8080 (http)
2025-05-23 10:42:17.938 INFO  main [org.apache.catalina.core.StandardService:?]-Starting service [Tomcat]
2025-05-23 10:42:17.938 INFO  main [org.apache.catalina.core.StandardEngine:?]-Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-05-23 10:42:17.993 INFO  main [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:?]-Initializing Spring embedded WebApplicationContext
2025-05-23 10:42:17.993 INFO  main [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:?]-Root WebApplicationContext: initialization completed in 3107 ms
2025-05-23 10:42:20.458 INFO  main [com.dstp.consumer.BatchMessageProcessor:?]-批处理器初始化完成，最大批量大小: 500, 处理间隔: 10秒, 线程池大小: 4
2025-05-23 10:42:21.494 INFO  main [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:?]-Exposing 5 endpoints beneath base path '/actuator'
2025-05-23 10:42:21.647 INFO  main [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:?]-Tomcat started on port 8080 (http) with context path '/'
2025-05-23 10:42:21.682 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-0
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:42:21.727 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:42:21.776 INFO  main [org.apache.kafka.common.security.authenticator.AbstractLogin:?]-Successfully logged in.
2025-05-23 10:42:21.840 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:42:21.841 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:42:21.841 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:42:21.841 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747968141840
2025-05-23 10:42:21.844 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Subscribed to topic(s): import_export_logistics_info
2025-05-23 10:42:21.850 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-1
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:42:21.851 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:42:21.860 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:42:21.860 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:42:21.860 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:42:21.860 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747968141860
2025-05-23 10:42:21.860 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Subscribed to topic(s): import_export_logistics_info
2025-05-23 10:42:21.862 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-2
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:42:21.862 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:42:21.869 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:42:21.869 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:42:21.869 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:42:21.869 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747968141869
2025-05-23 10:42:21.870 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Subscribed to topic(s): import_export_logistics_info
2025-05-23 10:42:21.872 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-3
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:42:21.872 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:42:21.878 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:42:21.878 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:42:21.878 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:42:21.878 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747968141878
2025-05-23 10:42:21.878 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Subscribed to topic(s): import_export_logistics_info
2025-05-23 10:42:21.894 INFO  main [com.dstp.Application:?]-Started Application in 7.605 seconds (process running for 8.688)
2025-05-23 10:42:21.909 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:42:21.915 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:42:21.917 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:42:21.919 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:42:21.921 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 0, 失败数: 0, 重试数: 0
2025-05-23 10:42:22.175 INFO  RMI TCP Connection(2)-********* [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:?]-Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-23 10:42:22.175 INFO  RMI TCP Connection(2)-********* [org.springframework.web.servlet.DispatcherServlet:?]-Initializing Servlet 'dispatcherServlet'
2025-05-23 10:42:22.177 INFO  RMI TCP Connection(2)-********* [org.springframework.web.servlet.DispatcherServlet:?]-Completed initialization in 2 ms
2025-05-23 10:42:22.177 INFO  RMI TCP Connection(3)-********* [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Starting...
2025-05-23 10:42:22.691 INFO  RMI TCP Connection(3)-********* [com.zaxxer.hikari.pool.HikariPool:?]-HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@116a22d2
2025-05-23 10:42:22.699 INFO  RMI TCP Connection(3)-********* [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Start completed.
2025-05-23 10:42:31.789 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node -1 due to socket connection setup timeout. The timeout value is 8742 ms.
2025-05-23 10:42:31.789 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 9033 ms.
2025-05-23 10:42:31.789 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 9499 ms.
2025-05-23 10:42:31.790 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -3 rack: null) disconnected
2025-05-23 10:42:31.790 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Bootstrap broker ***********:9094 (id: -1 rack: null) disconnected
2025-05-23 10:42:31.790 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -3 rack: null) disconnected
2025-05-23 10:42:41.225 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 9172 ms.
2025-05-23 10:42:41.225 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node -1 due to socket connection setup timeout. The timeout value is 8250 ms.
2025-05-23 10:42:41.225 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 8042 ms.
2025-05-23 10:42:41.225 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Bootstrap broker ***********:9094 (id: -1 rack: null) disconnected
2025-05-23 10:42:41.225 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -3 rack: null) disconnected
2025-05-23 10:42:41.225 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -2 rack: null) disconnected
2025-05-23 10:42:41.377 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:42:41.377 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:42:50.686 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 8733 ms.
2025-05-23 10:42:50.686 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -2 rack: null) disconnected
2025-05-23 10:42:50.842 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:42:50.847 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 8584 ms.
2025-05-23 10:42:50.847 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 11557 ms.
2025-05-23 10:42:50.847 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 10:42:50.847 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 10:43:00.244 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:43:00.245 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:43:00.245 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:43:00.246 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:43:00.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:43:09.687 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:43:09.687 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:43:09.693 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:09.693 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:09.694 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 18626 ms.
2025-05-23 10:43:09.694 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 8135 ms.
2025-05-23 10:43:09.694 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 10:43:09.694 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 10:43:09.849 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:09.976 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-2-79846599-9309-4a8d-ad62-d45f7ed28cfc
2025-05-23 10:43:09.977 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:10.003 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=63, memberId='dxc_sync_gel-2-79846599-9309-4a8d-ad62-d45f7ed28cfc', protocol='range'}
2025-05-23 10:43:10.008 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Finished assignment for group at generation 63: {dxc_sync_gel-2-79846599-9309-4a8d-ad62-d45f7ed28cfc=Assignment(partitions=[import_export_logistics_info-0])}
2025-05-23 10:43:10.037 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=63, memberId='dxc_sync_gel-2-79846599-9309-4a8d-ad62-d45f7ed28cfc', protocol='range'}
2025-05-23 10:43:10.037 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[import_export_logistics_info-0])
2025-05-23 10:43:10.038 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Adding newly assigned partitions: import_export_logistics_info-0
2025-05-23 10:43:10.088 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Found no committed offset for partition import_export_logistics_info-0
2025-05-23 10:43:19.136 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:19.138 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node 1 due to socket connection setup timeout. The timeout value is 8598 ms.
2025-05-23 10:43:19.138 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 9877 ms.
2025-05-23 10:43:19.138 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 10:43:19.258 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:43:19.260 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-3-8fa320f7-72a5-45de-9c4d-e313af4a5dfa
2025-05-23 10:43:19.260 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:19.528 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node 1 due to socket connection setup timeout. The timeout value is 8895 ms.
2025-05-23 10:43:19.555 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:43:28.596 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node 2 due to socket connection setup timeout. The timeout value is 8284 ms.
2025-05-23 10:43:28.720 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:28.846 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-0-ab4f9556-a61e-425e-8e54-b343b4a25bb3
2025-05-23 10:43:28.847 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:29.161 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:43:29.239 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.SubscriptionState:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting offset for partition import_export_logistics_info-0 to position FetchPosition{offset=211, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[***********:9094 (id: 1 rack: null)], epoch=0}}.
2025-05-23 10:43:29.240 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [import_export_logistics_info-0]
2025-05-23 10:43:29.240 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Revoke previously assigned partitions import_export_logistics_info-0
2025-05-23 10:43:29.240 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [import_export_logistics_info-0]
2025-05-23 10:43:29.241 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:29.266 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=64, memberId='dxc_sync_gel-2-79846599-9309-4a8d-ad62-d45f7ed28cfc', protocol='range'}
2025-05-23 10:43:29.266 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=64, memberId='dxc_sync_gel-3-8fa320f7-72a5-45de-9c4d-e313af4a5dfa', protocol='range'}
2025-05-23 10:43:29.266 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=64, memberId='dxc_sync_gel-0-ab4f9556-a61e-425e-8e54-b343b4a25bb3', protocol='range'}
2025-05-23 10:43:29.267 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Finished assignment for group at generation 64: {dxc_sync_gel-0-ab4f9556-a61e-425e-8e54-b343b4a25bb3=Assignment(partitions=[import_export_logistics_info-0]), dxc_sync_gel-2-79846599-9309-4a8d-ad62-d45f7ed28cfc=Assignment(partitions=[]), dxc_sync_gel-3-8fa320f7-72a5-45de-9c4d-e313af4a5dfa=Assignment(partitions=[])}
2025-05-23 10:43:29.295 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=64, memberId='dxc_sync_gel-0-ab4f9556-a61e-425e-8e54-b343b4a25bb3', protocol='range'}
2025-05-23 10:43:29.295 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=64, memberId='dxc_sync_gel-2-79846599-9309-4a8d-ad62-d45f7ed28cfc', protocol='range'}
2025-05-23 10:43:29.295 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=64, memberId='dxc_sync_gel-3-8fa320f7-72a5-45de-9c4d-e313af4a5dfa', protocol='range'}
2025-05-23 10:43:29.296 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:43:29.296 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:43:29.296 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[import_export_logistics_info-0])
2025-05-23 10:43:29.296 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:43:29.296 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:43:29.296 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Adding newly assigned partitions: import_export_logistics_info-0
2025-05-23 10:43:29.296 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:43:29.296 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:43:29.321 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Found no committed offset for partition import_export_logistics_info-0
2025-05-23 10:43:38.156 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:43:38.871 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.SubscriptionState:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Resetting offset for partition import_export_logistics_info-0 to position FetchPosition{offset=211, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[***********:9094 (id: 1 rack: null)], epoch=0}}.
2025-05-23 10:43:38.871 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [import_export_logistics_info-0]
2025-05-23 10:43:45.529 INFO  batch-processor-1 [com.dstp.consumer.BatchMessageProcessor:?]-处理Topic: import_export_logistics_info, 消息数量: 1
2025-05-23 10:43:47.603 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 10:43:47.603 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:47.731 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-1-2702a1b5-8a1d-4e2f-868d-cdaa363971a8
2025-05-23 10:43:47.731 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:47.811 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:43:47.811 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions import_export_logistics_info-0
2025-05-23 10:43:47.811 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [import_export_logistics_info-0]
2025-05-23 10:43:47.811 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:50.350 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:43:50.350 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:43:50.350 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Revoke previously assigned partitions 
2025-05-23 10:43:50.350 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Revoke previously assigned partitions 
2025-05-23 10:43:50.350 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: []
2025-05-23 10:43:50.350 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: []
2025-05-23 10:43:50.350 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:50.350 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:43:50.376 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=65, memberId='dxc_sync_gel-0-ab4f9556-a61e-425e-8e54-b343b4a25bb3', protocol='range'}
2025-05-23 10:43:50.376 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=65, memberId='dxc_sync_gel-1-2702a1b5-8a1d-4e2f-868d-cdaa363971a8', protocol='range'}
2025-05-23 10:43:50.376 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=65, memberId='dxc_sync_gel-3-8fa320f7-72a5-45de-9c4d-e313af4a5dfa', protocol='range'}
2025-05-23 10:43:50.376 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=65, memberId='dxc_sync_gel-2-79846599-9309-4a8d-ad62-d45f7ed28cfc', protocol='range'}
2025-05-23 10:43:50.376 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Finished assignment for group at generation 65: {dxc_sync_gel-1-2702a1b5-8a1d-4e2f-868d-cdaa363971a8=Assignment(partitions=[]), dxc_sync_gel-0-ab4f9556-a61e-425e-8e54-b343b4a25bb3=Assignment(partitions=[import_export_logistics_info-0]), dxc_sync_gel-2-79846599-9309-4a8d-ad62-d45f7ed28cfc=Assignment(partitions=[]), dxc_sync_gel-3-8fa320f7-72a5-45de-9c4d-e313af4a5dfa=Assignment(partitions=[])}
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=65, memberId='dxc_sync_gel-3-8fa320f7-72a5-45de-9c4d-e313af4a5dfa', protocol='range'}
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=65, memberId='dxc_sync_gel-1-2702a1b5-8a1d-4e2f-868d-cdaa363971a8', protocol='range'}
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=65, memberId='dxc_sync_gel-2-79846599-9309-4a8d-ad62-d45f7ed28cfc', protocol='range'}
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=65, memberId='dxc_sync_gel-0-ab4f9556-a61e-425e-8e54-b343b4a25bb3', protocol='range'}
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[import_export_logistics_info-0])
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Adding newly assigned partitions: import_export_logistics_info-0
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:43:50.404 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:43:50.429 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition import_export_logistics_info-0 to the committed offset FetchPosition{offset=212, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[***********:9094 (id: 1 rack: null)], epoch=0}}
2025-05-23 10:43:50.429 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [import_export_logistics_info-0]
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions import_export_logistics_info-0
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Member dxc_sync_gel-1-2702a1b5-8a1d-4e2f-868d-cdaa363971a8 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [import_export_logistics_info-0]
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Member dxc_sync_gel-0-ab4f9556-a61e-425e-8e54-b343b4a25bb3 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Member dxc_sync_gel-2-79846599-9309-4a8d-ad62-d45f7ed28cfc sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:46:15.068 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:46:15.069 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Member dxc_sync_gel-3-8fa320f7-72a5-45de-9c4d-e313af4a5dfa sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:46:15.069 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.069 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.069 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:46:15.071 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.071 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.071 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.071 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.071 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.071 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.071 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.071 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:46:15.100 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:46:15.100 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:46:15.100 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:46:15.100 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:46:15.100 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:46:15.100 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:46:15.100 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:46:15.100 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:46:15.100 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:46:15.100 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:46:15.100 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:46:15.100 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:46:15.112 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-3 unregistered
2025-05-23 10:46:15.112 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-2 unregistered
2025-05-23 10:46:15.113 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-1 unregistered
2025-05-23 10:46:15.113 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:46:15.113 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:46:15.113 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:46:15.310 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:46:15.310 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:46:15.310 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:46:15.310 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:46:15.317 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-0 unregistered
2025-05-23 10:46:15.317 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:46:15.318 INFO  SpringApplicationShutdownHook [org.springframework.boot.web.embedded.tomcat.GracefulShutdown:?]-Commencing graceful shutdown. Waiting for active requests to complete
2025-05-23 10:46:15.798 INFO  tomcat-shutdown [org.springframework.boot.web.embedded.tomcat.GracefulShutdown:?]-Graceful shutdown complete
2025-05-23 10:46:15.802 INFO  SpringApplicationShutdownHook [com.dstp.consumer.BatchMessageProcessor:?]-批处理器已关闭
2025-05-23 10:46:15.806 INFO  SpringApplicationShutdownHook [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Shutdown initiated...
2025-05-23 10:46:16.059 INFO  SpringApplicationShutdownHook [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Shutdown completed.
2025-05-23 10:46:19.856 INFO  main [com.dstp.Application:?]-Starting Application using Java ******** with PID 15600 (D:\workspace\dstp\project\dxc_sync_gel\target\classes started by zhenxing.chen in D:\workspace\dstp\project\dxc_sync_gel)
2025-05-23 10:46:19.858 INFO  main [com.dstp.Application:?]-The following 1 profile is active: "fat"
2025-05-23 10:46:21.323 INFO  main [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:?]-Tomcat initialized with port 8080 (http)
2025-05-23 10:46:21.334 INFO  main [org.apache.catalina.core.StandardService:?]-Starting service [Tomcat]
2025-05-23 10:46:21.334 INFO  main [org.apache.catalina.core.StandardEngine:?]-Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-05-23 10:46:21.383 INFO  main [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:?]-Initializing Spring embedded WebApplicationContext
2025-05-23 10:46:21.383 INFO  main [org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:?]-Root WebApplicationContext: initialization completed in 1475 ms
2025-05-23 10:46:23.536 INFO  main [com.dstp.consumer.BatchMessageProcessor:?]-批处理器初始化完成，最大批量大小: 500, 处理间隔: 10秒, 线程池大小: 4
2025-05-23 10:46:24.480 INFO  main [org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:?]-Exposing 5 endpoints beneath base path '/actuator'
2025-05-23 10:46:24.647 INFO  main [org.springframework.boot.web.embedded.tomcat.TomcatWebServer:?]-Tomcat started on port 8080 (http) with context path '/'
2025-05-23 10:46:24.697 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-0
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:46:24.739 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:46:24.777 INFO  main [org.apache.kafka.common.security.authenticator.AbstractLogin:?]-Successfully logged in.
2025-05-23 10:46:24.848 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:46:24.849 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:46:24.849 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:46:24.849 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747968384848
2025-05-23 10:46:24.852 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Subscribed to topic(s): import_export_logistics_info
2025-05-23 10:46:24.858 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-1
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:46:24.858 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:46:24.868 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:46:24.868 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:46:24.868 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:46:24.868 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747968384868
2025-05-23 10:46:24.868 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Subscribed to topic(s): import_export_logistics_info
2025-05-23 10:46:24.869 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-2
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:46:24.869 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:46:24.876 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:46:24.876 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:46:24.876 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:46:24.876 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747968384876
2025-05-23 10:46:24.877 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Subscribed to topic(s): import_export_logistics_info
2025-05-23 10:46:24.878 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [***********:9094, ************:9094, ************:9094]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = dxc_sync_gel-3
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = dxc_sync_gel
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metadata.recovery.strategy = none
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 600000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 30000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-05-23 10:46:24.878 INFO  main [org.apache.kafka.common.telemetry.internals.KafkaMetricsCollector:?]-initializing Kafka metrics collector
2025-05-23 10:46:24.884 INFO  main [org.apache.kafka.clients.consumer.ConsumerConfig:?]-These configurations '[max-poll-records]' were supplied but are not used yet.
2025-05-23 10:46:24.884 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka version: 3.8.1
2025-05-23 10:46:24.884 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka commitId: 70d6ff42debf7e17
2025-05-23 10:46:24.884 INFO  main [org.apache.kafka.common.utils.AppInfoParser:?]-Kafka startTimeMs: 1747968384884
2025-05-23 10:46:24.884 INFO  main [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Subscribed to topic(s): import_export_logistics_info
2025-05-23 10:46:24.899 INFO  main [com.dstp.Application:?]-Started Application in 5.565 seconds (process running for 6.057)
2025-05-23 10:46:24.910 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-标准商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:46:24.918 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-仓库缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:46:24.922 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-商品缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:46:24.926 INFO  scheduling-1 [com.dstp.service.BaseCacheService:?]-企业缓存统计 - 大小: 0, 命中率: 100.00%, 命中数: 0, 未命中数: 0, 加载成功数: 0, 加载失败数: 0, 加载时间: 0ms
2025-05-23 10:46:24.928 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 0, 失败数: 0, 重试数: 0
2025-05-23 10:46:25.162 INFO  RMI TCP Connection(2)-********* [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:?]-Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-23 10:46:25.162 INFO  RMI TCP Connection(2)-********* [org.springframework.web.servlet.DispatcherServlet:?]-Initializing Servlet 'dispatcherServlet'
2025-05-23 10:46:25.163 INFO  RMI TCP Connection(1)-********* [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Starting...
2025-05-23 10:46:25.165 INFO  RMI TCP Connection(2)-********* [org.springframework.web.servlet.DispatcherServlet:?]-Completed initialization in 3 ms
2025-05-23 10:46:25.598 INFO  RMI TCP Connection(1)-********* [com.zaxxer.hikari.pool.HikariPool:?]-HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@181caee8
2025-05-23 10:46:25.606 INFO  RMI TCP Connection(1)-********* [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Start completed.
2025-05-23 10:46:34.722 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 8150 ms.
2025-05-23 10:46:34.723 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 9773 ms.
2025-05-23 10:46:34.724 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -3 rack: null) disconnected
2025-05-23 10:46:34.724 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -3 rack: null) disconnected
2025-05-23 10:46:44.155 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 8249 ms.
2025-05-23 10:46:44.155 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -2 rack: null) disconnected
2025-05-23 10:46:44.312 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:46:44.312 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:46:44.312 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:46:44.312 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:46:53.718 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 8431 ms.
2025-05-23 10:46:53.718 WARN  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Bootstrap broker ************:9094 (id: -2 rack: null) disconnected
2025-05-23 10:46:53.763 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:46:53.763 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:46:53.782 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node -2 due to socket connection setup timeout. The timeout value is 10524 ms.
2025-05-23 10:46:53.782 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node -1 due to socket connection setup timeout. The timeout value is 11437 ms.
2025-05-23 10:47:03.154 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:47:03.162 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.Metadata:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Cluster ID: Wm7UQSEiTkeLVWCMbqItAw
2025-05-23 10:47:03.162 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:47:03.232 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 10904 ms.
2025-05-23 10:47:03.232 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 10530 ms.
2025-05-23 10:47:03.232 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 10:47:03.232 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 10:47:03.355 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:47:12.590 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:47:12.591 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:47:12.591 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 16433 ms.
2025-05-23 10:47:12.802 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:47:12.808 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 10:47:12.808 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:47:12.941 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf
2025-05-23 10:47:12.941 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:47:12.966 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=67, memberId='dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf', protocol='range'}
2025-05-23 10:47:12.971 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Finished assignment for group at generation 67: {dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf=Assignment(partitions=[import_export_logistics_info-0])}
2025-05-23 10:47:13.002 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=67, memberId='dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf', protocol='range'}
2025-05-23 10:47:13.002 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[import_export_logistics_info-0])
2025-05-23 10:47:13.004 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: import_export_logistics_info-0
2025-05-23 10:47:13.036 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition import_export_logistics_info-0 to the committed offset FetchPosition{offset=211, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[***********:9094 (id: 1 rack: null)], epoch=0}}
2025-05-23 10:47:13.036 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [import_export_logistics_info-0]
2025-05-23 10:47:18.576 INFO  batch-processor-1 [com.dstp.consumer.BatchMessageProcessor:?]-处理Topic: import_export_logistics_info, 消息数量: 1
2025-05-23 10:47:19.392 INFO  batch-processor-1 [com.dstp.consumer.BatchMessageProcessor:?]-Topic: import_export_logistics_info 处理完成, 消息数: 1, 耗时: 816ms, 平均: 816ms/条
2025-05-23 10:47:22.034 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:47:22.035 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 11811 ms.
2025-05-23 10:47:22.035 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node 2 due to socket connection setup timeout. The timeout value is 11278 ms.
2025-05-23 10:47:22.035 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 10:47:22.035 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node 2147483647 due to socket connection setup timeout. The timeout value is 8993 ms.
2025-05-23 10:47:22.035 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node -3 due to socket connection setup timeout. The timeout value is 23560 ms.
2025-05-23 10:47:22.035 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Group coordinator ************:9094 (id: 2147483647 rack: null) is unavailable or invalid due to cause: null. isDisconnected: true. Rediscovery will be attempted.
2025-05-23 10:47:22.036 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 10:47:22.239 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Disconnecting from node 1 due to socket connection setup timeout. The timeout value is 10096 ms.
2025-05-23 10:47:22.239 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 10:47:22.239 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:47:22.361 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-0-9713b8d5-9461-481d-8319-228cdf5353e2
2025-05-23 10:47:22.361 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:47:25.031 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:47:25.032 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Revoke previously assigned partitions import_export_logistics_info-0
2025-05-23 10:47:25.032 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [import_export_logistics_info-0]
2025-05-23 10:47:25.032 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:47:25.058 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=68, memberId='dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf', protocol='range'}
2025-05-23 10:47:25.058 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=68, memberId='dxc_sync_gel-0-9713b8d5-9461-481d-8319-228cdf5353e2', protocol='range'}
2025-05-23 10:47:25.058 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Finished assignment for group at generation 68: {dxc_sync_gel-0-9713b8d5-9461-481d-8319-228cdf5353e2=Assignment(partitions=[import_export_logistics_info-0]), dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf=Assignment(partitions=[])}
2025-05-23 10:47:25.086 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=68, memberId='dxc_sync_gel-0-9713b8d5-9461-481d-8319-228cdf5353e2', protocol='range'}
2025-05-23 10:47:25.086 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=68, memberId='dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf', protocol='range'}
2025-05-23 10:47:25.086 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:47:25.086 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[import_export_logistics_info-0])
2025-05-23 10:47:25.086 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:47:25.086 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Adding newly assigned partitions: import_export_logistics_info-0
2025-05-23 10:47:25.086 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:47:25.116 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition import_export_logistics_info-0 to the committed offset FetchPosition{offset=212, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[***********:9094 (id: 1 rack: null)], epoch=0}}
2025-05-23 10:47:25.116 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [import_export_logistics_info-0]
2025-05-23 10:47:31.599 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:47:41.031 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node 0 due to socket connection setup timeout. The timeout value is 9353 ms.
2025-05-23 10:47:41.031 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Disconnecting from node 1 due to socket connection setup timeout. The timeout value is 10001 ms.
2025-05-23 10:47:41.031 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: rebalance failed due to 'null' (DisconnectException)
2025-05-23 10:47:41.031 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:47:41.153 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-2-67030184-a02d-4e7e-97ff-bccafc9de490
2025-05-23 10:47:41.153 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:47:43.125 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:47:43.126 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Revoke previously assigned partitions 
2025-05-23 10:47:43.126 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: []
2025-05-23 10:47:43.126 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:47:43.608 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:47:43.608 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions import_export_logistics_info-0
2025-05-23 10:47:43.608 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [import_export_logistics_info-0]
2025-05-23 10:47:43.608 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:47:43.634 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=69, memberId='dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf', protocol='range'}
2025-05-23 10:47:43.634 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=69, memberId='dxc_sync_gel-0-9713b8d5-9461-481d-8319-228cdf5353e2', protocol='range'}
2025-05-23 10:47:43.634 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=69, memberId='dxc_sync_gel-2-67030184-a02d-4e7e-97ff-bccafc9de490', protocol='range'}
2025-05-23 10:47:43.635 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Finished assignment for group at generation 69: {dxc_sync_gel-2-67030184-a02d-4e7e-97ff-bccafc9de490=Assignment(partitions=[]), dxc_sync_gel-0-9713b8d5-9461-481d-8319-228cdf5353e2=Assignment(partitions=[import_export_logistics_info-0]), dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf=Assignment(partitions=[])}
2025-05-23 10:47:43.662 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=69, memberId='dxc_sync_gel-2-67030184-a02d-4e7e-97ff-bccafc9de490', protocol='range'}
2025-05-23 10:47:43.662 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=69, memberId='dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf', protocol='range'}
2025-05-23 10:47:43.662 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=69, memberId='dxc_sync_gel-0-9713b8d5-9461-481d-8319-228cdf5353e2', protocol='range'}
2025-05-23 10:47:43.662 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:47:43.662 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:47:43.662 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:47:43.662 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:47:43.662 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[import_export_logistics_info-0])
2025-05-23 10:47:43.662 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Adding newly assigned partitions: import_export_logistics_info-0
2025-05-23 10:47:43.662 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:47:43.662 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:47:43.688 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition import_export_logistics_info-0 to the committed offset FetchPosition{offset=212, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[***********:9094 (id: 1 rack: null)], epoch=0}}
2025-05-23 10:47:43.688 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [import_export_logistics_info-0]
2025-05-23 10:47:50.494 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Discovered group coordinator ************:9094 (id: 2147483647 rack: null)
2025-05-23 10:47:59.951 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:47:59.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Disconnecting from node 2 due to socket connection setup timeout. The timeout value is 16425 ms.
2025-05-23 10:48:00.075 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: need to re-join with the given member-id: dxc_sync_gel-1-3b947f62-7c62-4de7-a0de-027ecc0ad8e2
2025-05-23 10:48:00.075 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:48:01.695 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:48:01.695 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:48:01.695 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: group is already rebalancing
2025-05-23 10:48:01.695 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Revoke previously assigned partitions 
2025-05-23 10:48:01.695 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Revoke previously assigned partitions 
2025-05-23 10:48:01.695 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions import_export_logistics_info-0
2025-05-23 10:48:01.695 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: []
2025-05-23 10:48:01.695 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: []
2025-05-23 10:48:01.695 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [import_export_logistics_info-0]
2025-05-23 10:48:01.695 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:48:01.695 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:48:01.695 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] (Re-)joining group
2025-05-23 10:48:01.720 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=70, memberId='dxc_sync_gel-0-9713b8d5-9461-481d-8319-228cdf5353e2', protocol='range'}
2025-05-23 10:48:01.720 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=70, memberId='dxc_sync_gel-2-67030184-a02d-4e7e-97ff-bccafc9de490', protocol='range'}
2025-05-23 10:48:01.720 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=70, memberId='dxc_sync_gel-1-3b947f62-7c62-4de7-a0de-027ecc0ad8e2', protocol='range'}
2025-05-23 10:48:01.720 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully joined group with generation Generation{generationId=70, memberId='dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf', protocol='range'}
2025-05-23 10:48:01.720 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Finished assignment for group at generation 70: {dxc_sync_gel-2-67030184-a02d-4e7e-97ff-bccafc9de490=Assignment(partitions=[]), dxc_sync_gel-0-9713b8d5-9461-481d-8319-228cdf5353e2=Assignment(partitions=[import_export_logistics_info-0]), dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf=Assignment(partitions=[]), dxc_sync_gel-1-3b947f62-7c62-4de7-a0de-027ecc0ad8e2=Assignment(partitions=[])}
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=70, memberId='dxc_sync_gel-1-3b947f62-7c62-4de7-a0de-027ecc0ad8e2', protocol='range'}
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=70, memberId='dxc_sync_gel-0-9713b8d5-9461-481d-8319-228cdf5353e2', protocol='range'}
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=70, memberId='dxc_sync_gel-2-67030184-a02d-4e7e-97ff-bccafc9de490', protocol='range'}
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Successfully synced group in generation Generation{generationId=70, memberId='dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf', protocol='range'}
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[])
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Notifying assignor about the new Assignment(partitions=[import_export_logistics_info-0])
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Adding newly assigned partitions: 
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Adding newly assigned partitions: import_export_logistics_info-0
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:48:01.750 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: []
2025-05-23 10:48:01.775 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerUtils:?]-Setting offset for partition import_export_logistics_info-0 to the committed offset FetchPosition{offset=212, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[***********:9094 (id: 1 rack: null)], epoch=0}}
2025-05-23 10:48:01.776 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions assigned: [import_export_logistics_info-0]
2025-05-23 10:51:24.905 INFO  scheduling-1 [com.dstp.consumer.KafkaConsumer:?]-消息处理统计 - 成功数: 1, 失败数: 0, 重试数: 0
2025-05-23 10:51:44.804 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Node -3 disconnected.
2025-05-23 10:51:44.804 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.NetworkClient:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Node -1 disconnected.
2025-05-23 10:51:54.951 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Member dxc_sync_gel-1-3b947f62-7c62-4de7-a0de-027ecc0ad8e2 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:51:54.951 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Member dxc_sync_gel-2-67030184-a02d-4e7e-97ff-bccafc9de490 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerRebalanceListenerInvoker:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Revoke previously assigned partitions import_export_logistics_info-0
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: partitions revoked: [import_export_logistics_info-0]
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Member dxc_sync_gel-0-9713b8d5-9461-481d-8319-228cdf5353e2 sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Member dxc_sync_gel-3-a958cb00-d2bc-4993-b029-ae7e6bc2ffaf sending LeaveGroup request to coordinator ************:9094 (id: 2147483647 rack: null) due to the consumer unsubscribed from all topics
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.952 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.LegacyKafkaConsumer:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Unsubscribed all topics or patterns and assigned partitions
2025-05-23 10:51:54.954 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.954 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.954 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-3, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.954 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-2, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.954 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.954 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-0, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.954 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.954 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.clients.consumer.internals.ConsumerCoordinator:?]-[Consumer clientId=dxc_sync_gel-1, groupId=dxc_sync_gel] Request joining group due to: consumer pro-actively leaving the group
2025-05-23 10:51:54.984 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:51:54.984 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:51:54.984 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:51:54.984 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:51:54.984 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:51:54.984 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:51:54.984 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:51:54.984 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:51:54.988 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-1 unregistered
2025-05-23 10:51:54.988 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-2 unregistered
2025-05-23 10:51:54.988 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:51:54.989 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:51:55.007 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:51:55.007 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:51:55.007 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:51:55.007 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:51:55.009 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-3 unregistered
2025-05-23 10:51:55.009 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-3-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:51:55.165 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics scheduler closed
2025-05-23 10:51:55.165 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-05-23 10:51:55.165 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-05-23 10:51:55.165 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.metrics.Metrics:?]-Metrics reporters closed
2025-05-23 10:51:55.167 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.apache.kafka.common.utils.AppInfoParser:?]-App info kafka.consumer for dxc_sync_gel-0 unregistered
2025-05-23 10:51:55.168 INFO  org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1 [org.springframework.kafka.listener.KafkaMessageListenerContainer:?]-dxc_sync_gel: Consumer stopped
2025-05-23 10:51:55.169 INFO  SpringApplicationShutdownHook [org.springframework.boot.web.embedded.tomcat.GracefulShutdown:?]-Commencing graceful shutdown. Waiting for active requests to complete
2025-05-23 10:51:55.645 INFO  tomcat-shutdown [org.springframework.boot.web.embedded.tomcat.GracefulShutdown:?]-Graceful shutdown complete
2025-05-23 10:51:55.659 INFO  SpringApplicationShutdownHook [com.dstp.consumer.BatchMessageProcessor:?]-批处理器已关闭
2025-05-23 10:51:55.664 INFO  SpringApplicationShutdownHook [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Shutdown initiated...
2025-05-23 10:51:55.928 INFO  SpringApplicationShutdownHook [com.zaxxer.hikari.HikariDataSource:?]-HikariPool-1 - Shutdown completed.
