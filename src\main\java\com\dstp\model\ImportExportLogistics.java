package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.List;

/**
 * 进出口物流单据
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_import_export_logistics")
public class ImportExportLogistics extends BaseEntity {
    @NotBlank(message = "单据流水号不能为空")
    private String documentNo;
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    @NotNull(message = "客户ID不能为空")
    private Long customerId;
    @NotBlank(message = "客户名称不能为空")
    private String customerName;
    @NotNull(message = "单据类型不能为空")
    private Integer documentType;
    @NotBlank(message = "业务单号不能为空")
    private String businessNo;
    private Long carrierId;
    private String carrierCode;
    private String carrierName;
    private String billNo;
    private String customsDeclarationNo;
    private Long sourceEntId;
    private Long serverEntId;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    @TableField(exist = false, select = false)
    private List<ImportExportLogisticsContainer> containerList;
    @TableField(exist = false, select = false)
    private List<ImportExportLogisticsExpansion> expansionList;
    @TableField(exist = false, select = false)
    private List<ImportExportLogisticsTime> timeList;

    public String getUniqueKeyString() {
        if (documentNo == null) {
            return null;
        }
        return documentNo;
    }
}
