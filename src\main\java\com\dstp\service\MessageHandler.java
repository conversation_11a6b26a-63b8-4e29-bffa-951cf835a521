package com.dstp.service;

import com.dstp.mapper.MyBaseMapper;
import com.dstp.model.BaseEntity;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public abstract class MessageHandler {
    public abstract void process(JsonNode dataNode);

    /**
     * 去重
     * @param originList 原始数据集合
     * @return 去重后的数据集合
     * @param <T> 数据对象
     */
    protected <T extends BaseEntity> List<T> distinct(List<T> originList) {
        Map<String, T> distinctMap = new LinkedHashMap<>();
        for (T item : originList) {
            String key = item.getUniqueKeyString();
            if (key != null) {
                T existingItem = distinctMap.get(key);
                // 如果是新的key或者offset更大，则更新
                if (existingItem == null || (item.getKafkaOffset() != null && (existingItem.getKafkaOffset() == null
                        || item.getKafkaOffset() > existingItem.getKafkaOffset()))) {
                    distinctMap.put(key, item);
                }
            } else {
                log.warn("数据唯一索引中有字段为空，跳过处理！");
            }
        }
        return distinctMap.values().stream().toList();
    }

    /**
     * 批量设置父表的主键ID
     * @param originList 原始数据集合
     * @param mapper 父表Mapper
     * @return 回填了主键的父表集合
     * @param <T> 父表对象
     * @param <M> 父表Mapper
     */
    protected <T extends BaseEntity, M extends MyBaseMapper<T>> List<T> batchSetParentPkId(List<T> originList, M mapper) {

        // 查询已存在记录，通过getKeyExtractor提取唯一键
        List<T> existingList = mapper.selectByUniqueKeys(originList);

        // 构建ID映射（带冲突处理），键为唯一键，值为ID
        Map<String, Long> idMap = existingList.stream()
                .collect(Collectors.toMap(
                        BaseEntity::getUniqueKeyString, // 使用抽象方法获取键
                        BaseEntity::getId, // 直接使用BaseEntity的getId方法
                        (existing, replacement) -> existing
                ));

        // 回填ID并收集所有ID
        originList.forEach(item -> {
            String key = item.getUniqueKeyString();
            Long id = idMap.get(key);
            item.setId(id);
        });

        return originList;
    }

    /**
     * 批量获取父表主键ID
     * @param originList 原始数据集合
     * @param mapper 父表Mapper
     * @return 回填了主键的父表集合
     * @param <T> 父表对象
     * @param <M> 父表Mapper
     */
    protected <T extends BaseEntity, M extends MyBaseMapper<T>> List<Long> batchGetParentPkId(List<T> originList, M mapper) {
        return batchSetParentPkId(originList, mapper).stream()
                .map(BaseEntity::getId)
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * 批量设置子表的外键ID（父表和子表是一对多关系）
     * @param parentList 父表集合
     * @param childListGetter 子表数据获取器
     * @param childFkIdSetter 子表外键设置器
     * @return 子表集合
     * @param <P> 父表对象
     * @param <C> 子表对象
     */
    protected <P extends BaseEntity, C> List<C> batchSetManyChildFkId(
            List<P> parentList,
            Function<P, Collection<C>> childListGetter,
            BiConsumer<C, Long> childFkIdSetter) {

        // 提取所有子表数据并设置父ID
        return parentList.stream()
                .flatMap(parent -> {
                    Collection<C> childList = childListGetter.apply(parent);
                    if (!CollectionUtils.isEmpty(childList)) {
                        // 获取父ID并设置到子表对象
                        Long parentId = parent.getId();
                        childList.forEach(child -> childFkIdSetter.accept(child, parentId));
                        return childList.stream();
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * 批量设置子表的外键ID（父表和子表是一对一关系）
     * @param parentList 父表集合
     * @param childGetter 子表数据获取器
     * @param childFkIdSetter 子表外键设置器
     * @return 子表集合
     * @param <P> 父表对象
     * @param <C> 子表对象
     */
    protected <P extends BaseEntity, C> List<C> batchSetOneChildFkId(
            List<P> parentList,
            Function<P, C> childGetter,
            BiConsumer<C, Long> childFkIdSetter) {

        // 提取所有子表数据并设置父ID
        return parentList.stream()
                .flatMap(parent -> {
                    C child = childGetter.apply(parent);
                    if (child != null) {
                        // 获取父ID并设置到子表对象
                        Long parentId = parent.getId();
                        childFkIdSetter.accept(child, parentId);
                        return Stream.of(child);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .toList();
    }
}
