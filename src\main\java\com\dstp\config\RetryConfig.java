package com.dstp.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 重试配置类
 * 用于处理临时性错误，如网络波动、数据库连接问题等
 */
@Configuration
@EnableRetry
public class RetryConfig {

    /**
     * 创建重试模板
     * 使用指数退避策略，避免在短时间内频繁重试
     */
    @Bean
    public RetryTemplate retryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();
        
        // 设置退避策略（指数退避）
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(1000); // 初始间隔1秒
        backOffPolicy.setMultiplier(2.0);       // 每次重试间隔翻倍
        backOffPolicy.setMaxInterval(10000);    // 最大间隔10秒
        retryTemplate.setBackOffPolicy(backOffPolicy);
        
        // 设置重试策略
        // 对于特定异常类型进行重试，其他异常直接抛出
        Map<Class<? extends Throwable>, Boolean> retryableExceptions = new HashMap<>();
        retryableExceptions.put(org.springframework.dao.DataAccessException.class, true);
        retryableExceptions.put(org.springframework.transaction.TransactionException.class, true);
        retryableExceptions.put(java.net.ConnectException.class, true);
        retryableExceptions.put(java.net.SocketTimeoutException.class, true);
        retryableExceptions.put(java.io.IOException.class, true);
        retryableExceptions.put(java.sql.SQLException.class, true);
        
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy(3, retryableExceptions, true);
        retryTemplate.setRetryPolicy(retryPolicy);
        
        return retryTemplate;
    }
}
