<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.ImportExportLogisticsMapper">
    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="importExportLogisticsMap" type="com.dstp.model.ImportExportLogistics">
        <id property="id" column="id"/>
        <result property="documentNo" column="document_no"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="importExportLogisticsMap">
        SELECT id,document_no
        FROM doc_import_export_logistics
        WHERE document_no IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.documentNo}
        </foreach>
    </select>
</mapper>