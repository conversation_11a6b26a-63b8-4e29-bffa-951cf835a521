package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.*;
import com.dstp.mapper.*;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class InboundHandler extends MessageHandler {
    private final InboundMapper inboundMapper;
    private final InboundAttachmentMapper inboundAttachmentMapper;
    private final InboundContainerMapper inboundContainerMapper;
    private final InboundGoodsMapper inboundGoodsMapper;
    private final InboundVehicleMapper inboundVehicleMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<Inbound> inboundList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(inboundList)) {
            log.warn("入库数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        inboundList = distinct(inboundList);

        // 1. 新增或更新入库信息
        inboundMapper.batchUpsert(inboundList);

        // 2. 查询已存在的入库信息ID
        List<Long> inboundIds = batchGetParentPkId(inboundList, inboundMapper);

        // 3. 保存新入库单证信息
        List<InboundAttachment> inboundAttachmentList = batchSetManyChildFkId(inboundList,
                Inbound::getAttachmentList, InboundAttachment::setInboundOrderId);
        inboundAttachmentMapper.delete(new QueryWrapper<InboundAttachment>().in("inbound_order_id", inboundIds));
        inboundAttachmentMapper.batchInsert(inboundAttachmentList);

        // 4. 保存新入库货柜信息
        List<InboundContainer> inboundContainerList = batchSetManyChildFkId(inboundList,
                Inbound::getContainerList, InboundContainer::setInboundOrderId);
        inboundContainerMapper.delete(new QueryWrapper<InboundContainer>().in("inbound_order_id", inboundIds));
        inboundContainerMapper.batchInsert(inboundContainerList);

        // 5. 保存新入库商品信息
        List<InboundGoods> inboundGoodsList = batchSetManyChildFkId(inboundList,
                Inbound::getGoodsList, InboundGoods::setInboundOrderId);
        inboundGoodsMapper.delete(new QueryWrapper<InboundGoods>().in("inbound_order_id", inboundIds));
        inboundGoodsMapper.batchInsert(inboundGoodsList);

        // 6. 保存新入库车辆信息
        List<InboundVehicle> inboundVehicleList = batchSetManyChildFkId(inboundList,
                Inbound::getVehicleList, InboundVehicle::setInboundOrderId);
        inboundVehicleMapper.delete(new QueryWrapper<InboundVehicle>().in("inbound_order_id", inboundIds));
        inboundVehicleMapper.batchInsert(inboundVehicleList);
    }
}
