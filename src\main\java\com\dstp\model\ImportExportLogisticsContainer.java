package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 进出口物流单据-货柜
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_import_export_logistics_container")
public class ImportExportLogisticsContainer extends BaseEntity {
    @NotNull(message = "物流单ID不能为空")
    private Long documentId;
    private String containerNo;
    private String containerType;
    private String goodsName;
    private String goodsQuantity;
}
