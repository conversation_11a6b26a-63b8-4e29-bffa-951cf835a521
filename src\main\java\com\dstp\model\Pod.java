package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 抵港信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_pod_order")
public class Pod extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    @NotBlank(message = "企业订单号不能为空")
    private String entOrderNo;
    private String clientOrderNo;
    private String serviceOrderNo;
    private String busiOrderNo;
    private String contractNo;
    private String operateCode;
    private String operateName;
    private String tradeCountry;
    private String portNo;
    private String portsEntry;
    @NotNull(message = "接单时间不能为空")
    private LocalDateTime orderCreateTime;
    private LocalDateTime podTime;
    private LocalDateTime leaveTime;
    private LocalDateTime reportTime;
    private String transportMode;
    private String transportTool;
    private String voyageNo;
    private String billNo;
    private String superviseType;
    private String shipperNo;
    private String shipperName;
    private String consigneeNo;
    private String consigneeName;
    private Long sourceEntId;
    private Long serverEntId;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    private String linkId;

    public String getUniqueKeyString() {
        if (entId == null || entOrderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + entOrderNo + ":" + sourceSite;
    }
}
