package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 订舱单
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_booking_space")
public class BookingSpace extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    private String soNo;
    @NotBlank(message = "客户编码不能为空")
    private String customerCode;
    private String entrustName;
    private String shipper;
    private String consignee;
    private String carrierCode;
    private String vessel;
    private LocalDateTime bookDate;
    private String polName;
    private LocalDateTime enterDate;
    private LocalDateTime etdAtd;
    private String podName;
    private LocalDateTime etaAta;
    private String billNo;
    private LocalDateTime signDate;
    private Long sourceEntId;
    private String portCode;
    private String ieid;
    @NotBlank(message = "海运订阅状态不能为空")
    private String shippingState;
    @NotBlank(message = "港区订阅状态不能为空")
    private String portState;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    @NotBlank(message = "数据来源不能为空")
    private String dataSources;
    private String entryNo;
    private String containerType;
    private String containerNo;
    private String sealNo;

    @Override
    public String getUniqueKeyString() {
        if (entId == null || (soNo == null && billNo == null)) {
            return null;
        }
        return entId + ":" + (soNo == null ? "" : soNo) + ":" + (billNo == null ? "" : billNo) + ":" + (containerNo == null ? "" : containerNo);
    }
}