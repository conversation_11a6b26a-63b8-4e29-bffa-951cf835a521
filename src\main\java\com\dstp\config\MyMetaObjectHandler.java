package com.dstp.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class MyMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        // 插入操作时填充字段
        this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, LocalDateTime.now()); // 创建时间
        this.strictInsertFill(metaObject, "createdBy", String.class, getCurrentUserId()); // 创建人
        this.strictInsertFill(metaObject, "modifiedAt", LocalDateTime.class, LocalDateTime.now()); // 修改时间
        this.strictInsertFill(metaObject, "modifiedBy", String.class, getCurrentUserId()); // 修改人
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 更新操作时填充字段
        this.strictUpdateFill(metaObject, "modifiedAt", LocalDateTime.class, LocalDateTime.now()); // 修改时间
        this.strictUpdateFill(metaObject, "modifiedBy", String.class, getCurrentUserId()); // 修改人
    }

    private String getCurrentUserId() {
        // 获取当前用户 ID 的逻辑（如从服务器上下文或 ThreadLocal 获取）
        // 示例：假设用户 ID 存储在 ThreadLocal 中
        return "flink";
    }
}
