package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 汇率
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("exchange_rate")
public class ExchangeRate extends BaseEntity {
    @NotBlank(message = "汇兑币种编码不能为空")
    private String fromCode;
    @NotBlank(message = "汇兑币种名称不能为空")
    private String fromName;
    @NotBlank(message = "买入币种编码不能为空")
    private String toCode;
    @NotBlank(message = "买入币种名称不能为空")
    private String toName;
    private BigDecimal buyRate;
    private BigDecimal sellRate;
    @NotNull(message = "中间价不能为空")
    private BigDecimal referenceRate;
    private String dataSource;
    @NotNull(message = "汇率发布时间不能为空")
    private LocalDateTime publishedTime;
    @NotNull(message = "抓取时间不能为空")
    private LocalDateTime crawlTime;
    @NotNull(message = "是否发布不能为空")
    private Integer publishedStatus;
    @NotNull(message = "状态不能为空")
    private Integer status;
    @NotNull(message = "最后一次状态修改时间不能为空")
    private LocalDateTime statusAt;
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;
    @NotNull(message = "乐观锁不能为空")
    private Integer lastVersion;
}
