package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.StorageVoucherDetails;
import com.dstp.mapper.StorageVoucherDetailsMapper;
import com.dstp.mapper.StorageVoucherMapper;
import com.dstp.model.StorageVoucher;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class StorageVoucherHandler extends MessageHandler {
    private final StorageVoucherMapper storageVoucherMapper;
    private final StorageVoucherDetailsMapper storageVoucherDetailsMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<StorageVoucher> storageVoucherList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(storageVoucherList)) {
            log.warn("堆存数据为空，跳过处理！");
            return;
        }

        // 0. 堆存凭证编码拼接
        storageVoucherList.forEach(storageVoucher -> {
            // 拼接堆存凭证编码
            String busiOrderNo = storageVoucher.getBusiOrderNo();
            String serviceOrderNo = storageVoucher.getServiceOrderNo();
            Long pageCount = storageVoucher.getPageCount();
            LocalDateTime atTime = storageVoucher.getAtTime();
            if (StringUtils.isBlank(serviceOrderNo) && atTime != null && pageCount != null && !StringUtils.isBlank(busiOrderNo)) {
                String ymd = atTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
                if(pageCount > 1) {
                    storageVoucher.setStackingVoucherCode(busiOrderNo+ymd + "-" + pageCount);
                } else {
                    storageVoucher.setStackingVoucherCode(busiOrderNo+ymd);
                }
            } else {
                storageVoucher.setStackingVoucherCode(serviceOrderNo);
            }
        });

        // 1. 去重
        storageVoucherList = distinct(storageVoucherList);

        // 2. 新增或更新堆存信息
        storageVoucherMapper.batchUpsert(storageVoucherList);

        // 3. 查询已存在的堆存信息ID
        List<Long> storageVoucherIds = batchGetParentPkId(storageVoucherList, storageVoucherMapper);

        // 4. 保存新堆存商品信息
        List<StorageVoucherDetails> storageVoucherDetails = batchSetManyChildFkId(storageVoucherList,
                StorageVoucher::getBatchStockStatisticsVos, StorageVoucherDetails::setStorageVoucherId);
        storageVoucherDetailsMapper.delete(new QueryWrapper<StorageVoucherDetails>().in("storage_voucher_id", storageVoucherIds));
        storageVoucherDetailsMapper.batchInsert(storageVoucherDetails);
    }
}
