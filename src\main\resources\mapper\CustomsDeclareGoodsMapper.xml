<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.CustomsDeclareGoodsMapper">

    <update id="updateAttrByCustomsClearanceIds">
        update tb_transaction_customs_clearance_goods a
        left join tb_hs_country b on a.org_country = b.code
        left join tb_hs_country c on a.destination_country = c.code
        set
        a.nature_exemption = (
        case a.nature_exemption
        when '1' then 'NORMAL_TAXATION'
        when '2' then 'HALF_TAXATION'
        when '3' then 'FREE_TAXATION'
        when '4' then 'SPECIAL_CASE'
        when '5' then 'RANDOM_FREE_OR_TAXATION'
        when '6' then 'BOND'
        when '7' then 'GUARANTEE'
        when '8' then 'HALF_SUPPLEMENTARY_TAXATION'
        when '9' then 'FULL_TAX_REFUND'
        end
        ),
        a.org_country = coalesce(b.en_short, a.org_country),
        a.destination_country = coalesce(c.en_short, a.destination_country)
        where customs_clearance_id in
        <foreach item='id' collection='customsClearanceIds' open='(' separator=',' close=')'>
            #{id}
        </foreach>
    </update>
</mapper>