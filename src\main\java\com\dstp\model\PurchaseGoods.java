package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;

/**
 * 采购商品
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_purchase_goods")
public class PurchaseGoods extends BaseEntity {
    @NotNull(message = "采购单ID不能为空")
    private Long purchaseOrderId;
    private String goodsName;
    private String barCode;
    @NotNull(message = "采购数量不能为空")
    private BigDecimal qty;
    private BigDecimal priceTax;
    private BigDecimal price;
}

