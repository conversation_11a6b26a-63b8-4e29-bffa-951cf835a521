package com.dstp.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.dstp.mapper.injector.MySqlInjector;
import com.dstp.mapper.interceptor.EmptyParamInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MyBatisPlusConfig {

    @Bean
    public MySqlInjector mySqlInjector() {
        return new MySqlInjector();
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        return interceptor;
    }

    /**
     * 空参数拦截器，拦截写入参数为空自定义SQL方法，跳过SQL执行
     * @return 空参数拦截器
     */
    @Bean
    public EmptyParamInterceptor emptyParamInterceptor() {
        return new EmptyParamInterceptor();
    }
}