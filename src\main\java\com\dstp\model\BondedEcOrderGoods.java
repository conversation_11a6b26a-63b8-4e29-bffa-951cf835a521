package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyLongDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 保税电商订单商品
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_bonded_ec_order_goods")
public class BondedEcOrderGoods extends BaseEntity {
    @NotNull(message = "保税订单ID不能为空")
    private Long bondedOrderId;  // 关联主表ID
    @JsonProperty("gNum")
    private Integer gNum;
    private String entGoodsNo;
    private String entGoodsName;
    @NotNull(message = "商品数量不能为空")
    private Integer amount;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long price;
}
