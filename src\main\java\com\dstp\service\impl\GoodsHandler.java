package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.mapper.GoodsCateMapper;
import com.dstp.mapper.GoodsMapper;
import com.dstp.model.*;
import com.dstp.service.CommonGoodsService;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class GoodsHandler extends MessageHandler {
    private final CommonGoodsService commonGoodsService;
    private final GoodsMapper goodsMapper;
    private final GoodsCateMapper goodsCateMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<Goods> goodsList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(goodsList)) {
            log.warn("商品数据为空，跳过处理");
            return;
        }

        // 0. 去重
        goodsList = distinct(goodsList);

        // 1. 获取商品品类信息
        Map<String , CommonGoodsAttr> commonGoodsAttrMap =
                commonGoodsService.getGoodsAttrByBarcodeList(goodsList.stream().map(Goods::getBarCode).toList());

        // 2. 填充商品品类信息
        for (Goods goods : goodsList) {
            goods.setCurrency("CNY");
            // 获取商品品类
            CommonGoodsAttr attr = commonGoodsAttrMap.get(goods.getBarCode());
            if (attr != null) {
                goods.setBrand(attr.getBrand());
                goods.setFirstCategory(attr.getFirstCategory());
                goods.setSecondCategory(attr.getSecondCategory());
                goods.setThirdCategory(attr.getThirdCategory());
            }
        }

        // 3. 新增或更新物流商品信息
        goodsMapper.batchUpsert(goodsList);

        // 4. 查询已存在的保税电商交运ID
        List<Long> goodsIds = batchGetParentPkId(goodsList, goodsMapper);

        // 3. 保存新物流商品证书信息
        List<GoodsCate> goodsCateList = batchSetManyChildFkId(goodsList,
                Goods::getGoodsCateList, GoodsCate::setGoodsId);
        goodsCateMapper.delete(new QueryWrapper<GoodsCate>().in("goods_id", goodsIds));
        goodsCateMapper.batchInsert(goodsCateList);
    }
}
