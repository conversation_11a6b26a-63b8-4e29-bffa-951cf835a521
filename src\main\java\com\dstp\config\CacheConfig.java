package com.dstp.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 缓存配置类
 * 为不同类型的数据配置不同的缓存策略
 */
@Configuration
@EnableCaching
public class CacheConfig {

    @Value("${cache.default.max-size:1000}")
    private int defaultMaxSize;

    @Value("${cache.default.expire-after-access:3600}")
    private int defaultExpireAfterAccess;

    @Value("${cache.enterprise.max-size:500}")
    private int enterpriseMaxSize;

    @Value("${cache.enterprise.expire-after-access:7200}")
    private int enterpriseExpireAfterAccess;

    @Value("${cache.goods.max-size:2000}")
    private int goodsMaxSize;

    @Value("${cache.goods.expire-after-access:3600}")
    private int goodsExpireAfterAccess;

    @Value("${cache.warehouse.max-size:200}")
    private int warehouseMaxSize;

    @Value("${cache.warehouse.expire-after-access:7200}")
    private int warehouseExpireAfterAccess;

    @Value("${cache.exchange-rate.max-size:100}")
    private int exchangeRateMaxSize;

    @Value("${cache.exchange-rate.expire-after-access:86400}")
    private int exchangeRateExpireAfterAccess;

    /**
     * 默认缓存管理器
     */
    @Bean
    @Primary
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(defaultMaxSize)
                .expireAfterAccess(defaultExpireAfterAccess, TimeUnit.SECONDS)
                .recordStats());
        return cacheManager;
    }

    /**
     * 企业信息缓存管理器
     */
    @Bean
    public CacheManager enterpriseCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCacheNames(List.of("enterpriseCache"));
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(enterpriseMaxSize)
                .expireAfterAccess(enterpriseExpireAfterAccess, TimeUnit.SECONDS)
                .recordStats());
        return cacheManager;
    }

    /**
     * 商品信息缓存管理器
     */
    @Bean
    public CacheManager goodsCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCacheNames(List.of("goodsCache"));
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(goodsMaxSize)
                .expireAfterAccess(goodsExpireAfterAccess, TimeUnit.SECONDS)
                .recordStats());
        return cacheManager;
    }

    /**
     * 商品信息缓存管理器
     */
    @Bean
    public CacheManager commonGoodsCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCacheNames(List.of("commonGoodsCache"));
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(goodsMaxSize)
                .expireAfterAccess(goodsExpireAfterAccess, TimeUnit.SECONDS)
                .recordStats());
        return cacheManager;
    }

    /**
     * 仓库信息缓存管理器
     */
    @Bean
    public CacheManager warehouseCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCacheNames(List.of("warehouseCache"));
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(warehouseMaxSize)
                .expireAfterAccess(warehouseExpireAfterAccess, TimeUnit.SECONDS)
                .recordStats());
        return cacheManager;
    }

    /**
     * 汇率信息缓存管理器
     * 汇率数据变化较慢，可以缓存更长时间
     */
    @Bean
    public CacheManager exchangeRateCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCacheNames(List.of("exchangeRateCache"));
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(exchangeRateMaxSize)
                .expireAfterAccess(exchangeRateExpireAfterAccess, TimeUnit.SECONDS)
                .recordStats());
        return cacheManager;
    }
}
