package com.dstp.mapper;

import com.dstp.model.BookingSpace;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface BookingSpaceMapper extends MyBaseMapper<BookingSpace> {

    BookingSpace selectByUnique(@Param("entId")Long entId, @Param("soNo")String soNo,
                                @Param("billNo")String billNo, @Param("containerNo")String containerNo);

    boolean updateByUnique(BookingSpace bookingSpace);
}
