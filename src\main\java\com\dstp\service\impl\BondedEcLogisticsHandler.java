package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.BondedEcLogisticsGoods;
import com.dstp.mapper.BondedEcLogisticsGoodsMapper;
import com.dstp.mapper.BondedEcLogisticsMapper;
import com.dstp.model.BondedEcLogistics;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class BondedEcLogisticsHandler extends MessageHandler {

    private final BondedEcLogisticsMapper bondedEcLogisticsMapper;
    private final BondedEcLogisticsGoodsMapper bondedEcLogisticsGoodsMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode) {
        List<BondedEcLogistics> bondedEcLogisticsList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(bondedEcLogisticsList)) {
            log.warn("保税交运数据为空，跳过处理");
            return;
        }

        // 0. 去重
        bondedEcLogisticsList = distinct(bondedEcLogisticsList);

        // 1. 新增或更新保税电商交运
        bondedEcLogisticsMapper.batchUpsert(bondedEcLogisticsList);

        // 2. 查询已存在的保税电商交运ID
        List<Long> logisticsIds = batchGetParentPkId(bondedEcLogisticsList, bondedEcLogisticsMapper);

        // 3. 保存新保税电商交运商品
        List<BondedEcLogisticsGoods> bondedEcLogisticsGoodsList = batchSetManyChildFkId(bondedEcLogisticsList,
                BondedEcLogistics::getGoodsList, BondedEcLogisticsGoods::setLogisticsId);
        bondedEcLogisticsGoodsMapper.delete(new QueryWrapper<BondedEcLogisticsGoods>().in("logistics_id", logisticsIds));
        bondedEcLogisticsGoodsMapper.batchInsert(bondedEcLogisticsGoodsList);
    }
}