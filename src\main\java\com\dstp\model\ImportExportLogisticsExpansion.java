package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 进出口物流单据-拓展信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_import_export_logistics_expansion")
public class ImportExportLogisticsExpansion extends BaseEntity {
    @NotNull(message = "物流单ID不能为空")
    private Long documentId;
    @NotNull(message = "数据类型不能为空")
    private Integer dataType;
    private String value;
    private String path;
}
