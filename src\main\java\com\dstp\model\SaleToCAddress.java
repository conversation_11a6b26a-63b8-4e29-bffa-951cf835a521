package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * To C 销售地址
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_sale_order_to_c_address")
public class SaleToCAddress extends BaseEntity {
    @NotNull(message = "2C销售单ID不能为空")
    private Long saleOrderToCId;
    private String country;
    private String province;
    private String city;
    private String district;
}