package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 核注单清单
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_check_order_entity")
public class CheckEntity extends BaseEntity {
    @NotNull(message = "核注单ID不能为空")
    private Long orderId;  // 关联主表ID
    private String ecomOrder;
    private String entryNo;
}