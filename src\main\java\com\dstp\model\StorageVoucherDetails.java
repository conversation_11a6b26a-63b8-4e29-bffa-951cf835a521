package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.SizeDeserializer;
import com.dstp.deserializer.VolumeDeserializer;
import com.dstp.deserializer.WeightDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 堆存信息明细
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_storage_details")
public class StorageVoucherDetails extends BaseEntity {
    @NotNull(message = "堆存单ID不能为空")
    private Long storageVoucherId; // 关联字段：doc_storage_voucher.id
    @JsonDeserialize(using = SizeDeserializer.class)
    private BigDecimal productLength; // 长（厘米）
    @JsonDeserialize(using = SizeDeserializer.class)
    private BigDecimal productWidth; // 宽（厘米）
    @JsonDeserialize(using = SizeDeserializer.class)
    private BigDecimal productHeight; // 高（厘米）
    @JsonDeserialize(using = VolumeDeserializer.class)
    private BigDecimal productVolume; // 体积（立方厘米）
    private String productSpecification; // 规格
    @NotNull(message = "外箱长不能为空")
    @JsonDeserialize(using = SizeDeserializer.class)
    private BigDecimal packageLength; // 外箱长（厘米）
    @NotNull(message = "外箱宽不能为空")
    @JsonDeserialize(using = SizeDeserializer.class)
    private BigDecimal packageWidth; // 外箱宽（厘米）
    @NotNull(message = "外箱高不能为空")
    @JsonDeserialize(using = SizeDeserializer.class)
    private BigDecimal packageHeight; // 外箱高（厘米）
    @NotNull(message = "外箱体积不能为空")
    @JsonDeserialize(using = VolumeDeserializer.class)
    private BigDecimal packageVolume; // 外箱体积（立方厘米）
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal packageWeight; // 包装重量（克）
    private BigDecimal packageNum; // 箱装数
    @JsonDeserialize(using = VolumeDeserializer.class)
    private BigDecimal avgPackageVolume; // 按箱平摊的商品体积(立方厘米)
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal weightWithMaterial; // 带耗材重量（克）
    @JsonProperty("gCode")
    private String gCode; // 货号
    private String productCname; // 商品名
    private BigDecimal palletNum; // 托盘主单位数
    private String warehouseId; // 仓库ID
    private String warehouseType; // 仓库类型:10：普通仓;20：恒温仓;30：冷链仓;40：危化仓
    private String stockBusinessType; // 库存业务模式
    private String batchCode; // 客户批次号
    private String originBatchCode; // 原始批次号
    @NotNull(message = "堆存计费起始时间不能为空")
    private LocalDateTime firstReceiveDate; // 开始堆存计费时间
    private String isDamaged; // 是否坏品:0否 1是
    private String isColdStorage; // 是否温控库:0否 1是
    private BigDecimal temperature; // 温控库温度,是否温控为1时必填
    @NotNull(message = "库存数不能为空")
    private BigDecimal stockCount; // 库存数
    private String hscode; // Hs编码
}
