package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 异常日志实体类
 * 用于记录系统运行过程中的异常信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("tb_dstp_dxc_sync_err")
public class ExceptionLog extends BaseEntity {
    /** 异常简短消息 */
    private String message;

    /** Kafka主题 */
    private String topic;

    /** Kafka偏移量 */
    private Long offset;

    /** 异常详细内容，包括堆栈信息 */
    private String content;

    /** 异常类型：ERROR、WARN、INFO等 */
    private String type;
}
