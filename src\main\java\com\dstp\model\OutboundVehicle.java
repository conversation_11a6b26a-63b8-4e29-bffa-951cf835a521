package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 出库车辆
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_outbound_vehicle")
public class OutboundVehicle extends BaseEntity {
    @NotNull(message = "出库单ID不能为空")
    private Long outboundOrderId;
    @JsonProperty("gNum")
    private String gNum;
    private String vehicleNo;
    private String vehicleType;
    private String notes;
}