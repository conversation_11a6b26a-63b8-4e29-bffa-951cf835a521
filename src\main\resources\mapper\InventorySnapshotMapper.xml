<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.InventorySnapshotMapper">
    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="inventorySnapshotMap" type="com.dstp.model.InventorySnapshot">
        <id property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="atTime" column="at_time"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="inventorySnapshotMap">
        SELECT id,ent_id,at_time
        FROM doc_inventory_snapshot
        WHERE (ent_id,at_time) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.entId}, #{item.atTime})
        </foreach>
    </select>
</mapper>