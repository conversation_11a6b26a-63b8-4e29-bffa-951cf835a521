package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.*;
import com.dstp.mapper.SaleToBGoodsMapper;
import com.dstp.mapper.SaleToBMapper;
import com.dstp.mapper.SaleToBPurchaseMapper;
import com.dstp.service.CommonGoodsService;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class SaleToBHandler extends MessageHandler {
    private final SaleToBMapper saleToBMapper;
    private final SaleToBGoodsMapper saleToBGoodsMapper;
    private final SaleToBPurchaseMapper saleToBPurchaseMapper;
    private final CommonGoodsService commonGoodsService;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<SaleToB> saleToBList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(saleToBList)) {
            log.warn("销售2B数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        saleToBList = distinct(saleToBList);

        // 1. 新增或更新ToB销售信息
        saleToBMapper.batchUpsert(saleToBList);

        // 2. 查询已存在的保税电商交运ID
        List<Long> saleIds = batchGetParentPkId(saleToBList, saleToBMapper);

        // 3. 保存新ToB销售商品信息
        Map<String, CommonGoodsAttr> commonGoodsAttrMap = commonGoodsService.getGoodsAttrByBarcodeList(
                saleToBList.stream()
                        .flatMap(saleToB -> Optional.ofNullable(saleToB.getGoodsList())
                                .orElse(List.of())
                                .stream()
                                .map(SaleToBGoods::getBarCode))
                        .toList());

        List<SaleToBGoods> saleToBGoodsList = saleToBList.stream()
                .flatMap(saleToB -> Optional.ofNullable(saleToB.getGoodsList())
                        .orElse(List.of())
                        .stream()
                        .peek(saleToBGoods -> {
                            saleToBGoods.setSaleOrderId(saleToB.getId());
                            // 获取商品品类
                            CommonGoodsAttr attr = commonGoodsAttrMap.get(saleToBGoods.getBarCode());
                            if (attr != null) {
                                saleToBGoods.setCategory1(attr.getFirstCategory());
                                saleToBGoods.setCategory2(attr.getSecondCategory());
                            }
                        }))
                .toList();
        saleToBGoodsMapper.delete(new QueryWrapper<SaleToBGoods>().in("sale_order_id", saleIds));
        saleToBGoodsMapper.batchInsert(saleToBGoodsList);

        // 4. 保存新ToB销售采购信息
        List<SaleToBPurchase> saleToBPurchaseList = batchSetManyChildFkId(saleToBList,
                SaleToB::getPurchaseList, SaleToBPurchase::setSaleOrderId);
        saleToBPurchaseMapper.delete(new QueryWrapper<SaleToBPurchase>().in("sale_order_id", saleIds));
        saleToBPurchaseMapper.batchInsert(saleToBPurchaseList);
    }
}
