package com.dstp.model;

import java.io.Serial;

/**
 * 四元组实现
 */
public class Tuple4<T0, T1, T2, T3> extends Tuple {

    @Serial
    private static final long serialVersionUID = 1L;

    public T0 f0;
    public T1 f1;
    public T2 f2;
    public T3 f3;

    public Tuple4() {
    }

    public Tuple4(T0 f0, T1 f1, T2 f2, T3 f3) {
        this.f0 = f0;
        this.f1 = f1;
        this.f2 = f2;
        this.f3 = f3;
    }

    @Override
    public int getArity() {
        return 4;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getField(int pos) {
        return switch (pos) {
            case 0 -> (T) f0;
            case 1 -> (T) f1;
            case 2 -> (T) f2;
            case 3 -> (T) f3;
            default -> throw new IndexOutOfBoundsException("索引越界: " + pos);
        };
    }

    @Override
    @SuppressWarnings("unchecked")
    protected <T> void setField(int pos, T value) {
        switch (pos) {
            case 0: f0 = (T0) value; break;
            case 1: f1 = (T1) value; break;
            case 2: f2 = (T2) value; break;
            case 3: f3 = (T3) value; break;
            default: throw new IndexOutOfBoundsException("索引越界: " + pos);
        }
    }
}
