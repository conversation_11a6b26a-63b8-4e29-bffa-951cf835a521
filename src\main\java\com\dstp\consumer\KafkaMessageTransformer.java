package com.dstp.consumer;

import com.dstp.model.Enterprise;
import com.dstp.model.Metadata;
import com.dstp.service.EnterpriseService;
import com.dstp.service.ExceptionService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.function.BiFunction;

@Slf4j
@Component
@RequiredArgsConstructor
public class KafkaMessageTransformer {
    private final EnterpriseService enterpriseService;
    private final ExceptionService exceptionService;
    private final ObjectMapper objectMapper;

    /**
     * 调整Json消息结构
     *
     * @param message 消息
     * @return 消息 JsonNode 休息
     */
    public JsonNode singleTransformer(ConsumerRecord<String, String> message) {
        if (message == null) {
            return objectMapper.createObjectNode();
        }

        // 首先提取所有消息中的MDM编码，然后批量查询企业信息
        List<ConsumerRecord<String, String>> messageList = new ArrayList<>();
        messageList.add(message);
        Map<String, Enterprise> enterpriseCache = preloadEnterprises(messageList);
        return transformer(message, enterpriseCache);
    }

    /**
     * 批量调整Json消息结构，将没批消息存为数组。
     * 使用批量查询企业信息以提高性能。
     *
     * @param messageList 消息列表
     * @return 包含所有消息的 ArrayNode 对象
     */
    public JsonNode batchTransformer(List<ConsumerRecord<String, String>> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return objectMapper.createArrayNode();
        }

        // 首先提取所有消息中的MDM编码，然后批量查询企业信息
        Map<String, Enterprise> enterpriseCache = preloadEnterprises(messageList);

        // 使用预加载的企业信息处理每个消息
        ArrayNode dataArray = objectMapper.createArrayNode();
        for (ConsumerRecord<String, String> message : messageList) {
            JsonNode node = transformer(message, enterpriseCache);
            if (node != null) {
                dataArray.add(node);
            }
        }
        return dataArray;
    }

    /**
     * 调整单个Json消息结构，处理元数据和主数据。
     * 可以使用预加载的企业信息缓存。
     *
     * @param message 消息记录
     * @param enterpriseCache 预加载的企业信息缓存，如果为null则直接查询数据库
     * @return 处理后的 JsonNode，如果处理失败则返回 null
     */
    public JsonNode transformer(ConsumerRecord<String, String> message, Map<String, Enterprise> enterpriseCache) {
        String topic = message.topic();
        Long offset = message.offset();
        String value = message.value();

        try {
            JsonNode root = objectMapper.readTree(value);

            // 1. 处理元数据
            JsonNode metadataNode = root.get("metadata");
            Metadata metadata = convertMetadata(metadataNode, enterpriseCache);
            if (metadata == null) {
                exceptionService.warn("匹配不到企业信息！", topic, offset);
                return null;
            }
            if (topic.equals("dxc_logistics_customs_clearance") && StringUtils.isBlank(metadata.getEntLicenseNo())) {
                exceptionService.warn("报关单匹配不到企业营业证件号！", topic, offset);
                return null;
            }

            // 2. 处理主数据
            JsonNode dataNode = root.get("data");
            metadata.setKafkaOffset(offset);
            return convertData(topic, metadata, dataNode);
        } catch (JsonProcessingException e) {
            exceptionService.alarm("JSON数据解析失败！", e, topic, offset);
        } catch (Exception e) {
            exceptionService.alarm("业务数据处理失败！", e, topic, offset);
        }
        return null;
    }

    /**
     * 从消息列表中提取所有的mdmCode，并批量查询企业信息
     *
     * @param messageList 消息列表
     * @return 企业信息映射，key为mdmCode，value为企业信息
     */
    private Map<String, Enterprise> preloadEnterprises(List<ConsumerRecord<String, String>> messageList) {
        Set<String> mdmCodes = new HashSet<>();

        // 从所有消息中提取MDM编码
        for (ConsumerRecord<String, String> message : messageList) {
            try {
                JsonNode root = objectMapper.readTree(message.value());
                JsonNode metadataNode = root.get("metadata");
                if (metadataNode != null && !metadataNode.isEmpty()) {
                    // 提取客户、服务提供商和应用服务提供商的MDM编码
                    String customerMdm = metadataNode.path("subjects").path("customer").asText();
                    String serviceProviderMdm = metadataNode.path("subjects").path("serviceProvider").asText();
                    String appServiceProviderMdm = metadataNode.path("subjects").path("appServiceProvider").asText();

                    if (StringUtils.isNotBlank(customerMdm)) {
                        mdmCodes.add(customerMdm);
                    }
                    if (StringUtils.isNotBlank(serviceProviderMdm)) {
                        mdmCodes.add(serviceProviderMdm);
                    }
                    if (StringUtils.isNotBlank(appServiceProviderMdm)) {
                        mdmCodes.add(appServiceProviderMdm);
                    }
                }
            } catch (Exception e) {
                // 如果解析失败，记录日志并继续处理下一条消息
                log.warn("提取MDM编码失败: {}", e.getMessage());
            }
        }

        log.debug("批量查询企业信息，编码数量: {}", mdmCodes.size());
        return enterpriseService.getEnterpriseByMdmCodeList(new ArrayList<>(mdmCodes));
    }

    /**
     * 将 JsonNode 转换为 Metadata 对象。
     *
     * @param metadataNode 元数据 JsonNode
     * @return Metadata 对象，如果转换失败则返回 null
     */
    public Metadata convertMetadata(JsonNode metadataNode, Map<String, Enterprise> enterpriseCache) {
        if (metadataNode == null || metadataNode.isEmpty()) {
            log.warn("Kafka消息中Metadata元素数据结构无效！Metadata: {}", metadataNode);
            return null;
        }

        Metadata metadata = new Metadata();
        String entMdmCode = metadataNode.path("subjects").path("customer").asText();
        String sourceEntMdmCode = metadataNode.path("subjects").path("appServiceProvider").asText();
        String serverEntMdmCode = metadataNode.path("subjects").path("serviceProvider").asText();
        String inventoryDate = metadataNode.path("date").asText();
        String sourceSite = metadataNode.path("site").asText();
        String inventoryVersion = metadataNode.path("batchCode").asText();

        if (!StringUtils.isBlank(entMdmCode)) {
            Enterprise enterprise = enterpriseCache.get(entMdmCode);
            if (enterprise == null) return null;
            metadata.setEntId(enterprise.getId());
            metadata.setEntName(enterprise.getName());
            metadata.setEntLicenseNo(enterprise.getLicenseNo());
        }
        if (!StringUtils.isBlank(sourceEntMdmCode)) {
            Enterprise sourceEnterprise = enterpriseCache.get(sourceEntMdmCode);
            if (sourceEnterprise != null) {
                metadata.setSourceEntId(sourceEnterprise.getId());
                metadata.setSourceEntName(sourceEnterprise.getName());
                metadata.setSourceEntLicenseNo(sourceEnterprise.getLicenseNo());
            }
        }
        if (!StringUtils.isBlank(serverEntMdmCode)) {
            Enterprise serverEnterprise = enterpriseCache.get(serverEntMdmCode);
            if (serverEnterprise != null) {
                metadata.setServerEntId(serverEnterprise.getId());
                metadata.setServerEntName(serverEnterprise.getName());
                metadata.setServerEntLicenseNo(serverEnterprise.getLicenseNo());
            }
        }
        metadata.setSourceSite(StringUtils.isBlank(sourceSite) ? null : sourceSite);
        metadata.setInventoryVersion(StringUtils.isBlank(inventoryVersion) ? null : inventoryVersion);
        metadata.setInventoryDate(StringUtils.isBlank(inventoryDate) ? null : LocalDate.parse(inventoryDate).atStartOfDay());

        return metadata;
    }

    /**
     * 根据主题和元数据转换数据节点。
     *
     * @param topic    主题
     * @param metadata 元数据对象
     * @param dataNode 数据节点
     * @return 转换后的 JsonNode，如果转换失败则返回 null
     */
    public JsonNode convertData(String topic, Metadata metadata, JsonNode dataNode) {
        if (dataNode == null || dataNode.isEmpty()) {
            log.warn("无效的输入数据, Data: {}", dataNode);
            return null;
        }

        ObjectNode metadataNode = objectMapper.valueToTree(metadata);
        return dataConverters.getOrDefault(topic, (data, meta) -> ((ObjectNode) data).setAll(meta)).apply(dataNode, metadataNode);
    }

    private final Map<String, BiFunction<JsonNode, ObjectNode, JsonNode>> dataConverters = Map.of(
            "dxc_info_inventory_query", this::inventoryConvert,
            "dxc_logistics_booking_space", this::bookingSpaceConvert,
            "dxc_logistics_goods", this::logisticsGoodsConvert,
            "dxc_trade_sale_toc", this::saleToCConvert,
            "dxc_trade_synchronize_goods", this::tradeGoodsConvert
    );

    // 重构库存数据结构
    public JsonNode inventoryConvert(JsonNode dataNode, ObjectNode metadataNode) {
        // 每个库存添加元数据信息
        ArrayNode inventoryList = (ArrayNode) dataNode;
        inventoryList.forEach(inventory -> ((ObjectNode) inventory).setAll(metadataNode));
        return inventoryList;
    }

    // 重构物流商品源数据结构
    public JsonNode logisticsGoodsConvert(JsonNode dataNode, ObjectNode metadataNode) {
        if (!dataNode.has("goodsList") || dataNode.get("goodsList").isEmpty()) {
            return null;
        }

        // 每个商品添加元数据信息
        ArrayNode goodsList = (ArrayNode) dataNode.get("goodsList");
        goodsList.forEach(goods -> ((ObjectNode) goods).setAll(metadataNode));
        return goodsList;
    }

    // 重构商贸商品源数据结构
    public JsonNode tradeGoodsConvert(JsonNode dataNode, ObjectNode metadataNode) {
        if (!dataNode.has("recordInfo") || dataNode.get("recordInfo").isEmpty()) {
            return null;
        }

        // 将除recordInfo以外的元素添加到metadataNode中
        dataNode.fields().forEachRemaining(entry -> {
            if (!"recordInfo".equals(entry.getKey())) {
                // 商品基础信息图片地址只取第一个
                if ("picUrl".equals(entry.getKey())) {
                    metadataNode.set(entry.getKey(), entry.getValue().get(0));
                } else if ("goodsName".equals(entry.getKey())) {
                    metadataNode.set(entry.getKey(), entry.getValue());
                    metadataNode.set("entGoodsName", entry.getValue());
                } else {
                    metadataNode.set(entry.getKey(), entry.getValue());
                }
            }
        });

        // 每个商品添加元数据信息
        ArrayNode goodsList = (ArrayNode) dataNode.get("recordInfo");
        goodsList.forEach(goods -> {
            ((ObjectNode) goods).setAll(metadataNode);
            ((ObjectNode) goods).set("goodsNo", goods.get("entGoodsNo"));
            ((ObjectNode) goods).set("price", goods.get("recordPrice"));
        });
        return goodsList;
    }

    // 重构订舱单源数据结构，将订舱单根据箱号拆分成多条
    public JsonNode bookingSpaceConvert(JsonNode dataNode, ObjectNode metadataNode) {
        if (!dataNode.has("containers") || dataNode.get("containers").isEmpty()) {
            return ((ObjectNode) dataNode).setAll((metadataNode));
        }

        // 将除containers以外的元素添加到metadataNode中
        dataNode.fields().forEachRemaining(entry -> {
            if (!"containers".equals(entry.getKey())) {
                metadataNode.set(entry.getKey(), entry.getValue());
            }
        });

        // 每个箱号添加元数据信息
        ArrayNode containerList = (ArrayNode) dataNode.get("containers");
        containerList.forEach(container -> ((ObjectNode) container).setAll(metadataNode));
        return containerList;
    }

    // 重构销售2C源数据结构
    public JsonNode saleToCConvert(JsonNode dataNode, ObjectNode metadataNode) {
        if (!dataNode.has("orderList") || dataNode.get("orderList").isEmpty()) {
            return null;
        }

        // 每个订单添加到元数据信息
        ArrayNode orderList = (ArrayNode) dataNode.get("orderList");
        orderList.forEach(order -> ((ObjectNode) order).setAll(metadataNode));
        return orderList;
    }
}
