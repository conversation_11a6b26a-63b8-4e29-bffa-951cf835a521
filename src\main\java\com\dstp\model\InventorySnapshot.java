package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存快照
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_inventory_snapshot")
public class InventorySnapshot extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    @NotNull(message = "快照时点不能为空")
    private LocalDateTime atTime;
    @TableField(exist = false, select = false)
    private List<InventorySnapshotDetail> inventoryList;

    public String getUniqueKeyString() {
        if (entId == null || atTime == null) {
            return null;
        }
        return entId + ":" + atTime;
    }
}
