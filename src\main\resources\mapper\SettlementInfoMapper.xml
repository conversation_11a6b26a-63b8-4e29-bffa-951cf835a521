<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.SettlementInfoMapper">
    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="settlementInfoMap" type="com.dstp.model.Settlement">
        <id property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="orderNo" column="order_no"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="settlementInfoMap">
        SELECT id,ent_id,order_no
        FROM doc_settlement_info
        WHERE (ent_id,order_no) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.entId}, #{item.orderNo})
        </foreach>
    </select>
</mapper>