package com.dstp.consumer;

import com.dstp.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.CommitFailedException;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.dao.DataAccessException;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import java.net.SocketTimeoutException;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Kafka消息消费者
 * 负责接收并处理Kafka消息
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaConsumer {

    private final ExceptionService exceptionService;
    private final BatchMessageProcessor batchProcessor;
    private final SingleMessageProcessor singleProcessor;
    private final RetryTemplate retryTemplate;

    // 记录重试次数
    private final AtomicInteger retryCount = new AtomicInteger(0);
    private final AtomicInteger successCount = new AtomicInteger(0);
    private final AtomicInteger failureCount = new AtomicInteger(0);

    /**
     * 监听并处理Kafka消息
     * 支持批量处理和单条处理
     */
    @KafkaListener(topics = "#{'${spring.kafka.consumer.topic}'.split(',')}")
    public void listen(ConsumerRecord<String, String> message, Acknowledgment acknowledgment) {
        // 消息为空，直接返回
        if (message == null) {
            log.warn("Kafka消息为空");
            acknowledgment.acknowledge();
            return;
        }

        String topic = message.topic();
        Long offset = message.offset();
        String key = message.key();

        log.debug("接收到Kafka消息: topic={}, offset={}, key={}", topic, offset, key);

        try {
            // 使用重试模板处理消息
            retryTemplate.execute((RetryCallback<Void, Exception>) context -> {
                int retryCount = context.getRetryCount();
                if (retryCount > 0) {
                    log.warn("消息处理重试: topic={}, offset={}, 重试次数={}", topic, offset, retryCount);
                    this.retryCount.incrementAndGet();
                }

                processMessage(topic, message);
                return null;
            }, context -> {
                // 所有重试失败后的恢复处理
                log.error("消息处理失败，重试耗尽: topic={}, offset={}, 异常={}",
                        topic, offset, context.getLastThrowable().getMessage());
                failureCount.incrementAndGet();
                return null;
            });

            // 处理成功
            successCount.incrementAndGet();
            acknowledgment.acknowledge();

        } catch (CommitFailedException e) {
            exceptionService.alarm("Kafka偏移量提交失败！", e, topic, offset);
            // 对于提交失败，仍然尝试确认，避免消息重复消费
            acknowledgment.acknowledge();

        } catch (Exception e) {
            // 区分处理不同类型的异常
            String errorType = "未知异常";
            if (e instanceof DataAccessException) {
                errorType = "数据库访问异常";
            } else if (e instanceof SQLException) {
                errorType = "SQL异常";
            } else if (e instanceof SocketTimeoutException) {
                errorType = "网络超时";
            } else if (e instanceof IllegalArgumentException) {
                errorType = "参数异常";
            }

            // 使用新的异常处理方法，直接传递异常对象
            exceptionService.alarm(errorType + ",Kafka数据处理失败！", e, topic, offset);

            // 对于严重错误，确认消息已处理，避免重复消费导致同样的错误
            acknowledgment.acknowledge();
            failureCount.incrementAndGet();
        }
    }

    /**
     * 处理消息，根据主题决定使用批量处理或单条处理
     */
    private void processMessage(String topic, ConsumerRecord<String, String> message) {
        // 需要单条处理的主题列表
        String singleProcessTopics = "dxc_trade_sale_toc,dxc_info_inventory_query,dxc_logistics_booking_space,dxc_logistics_goods,dxc_trade_synchronize_goods";
        List<String> singleTopics = Arrays.asList(singleProcessTopics.split(","));

        if (singleTopics.contains(topic)) {
            // 单条处理
            singleProcessor.singleProcess(topic, message);
        } else {
            // 批量处理
            batchProcessor.addToBatch(topic, message);
        }
    }

    /**
     * 定时打印消息处理统计信息
     */
    @org.springframework.scheduling.annotation.Scheduled(fixedRate = 300000) // 每5分钟打印一次
    public void printStats() {
        log.info("消息处理统计 - 成功数: {}, 失败数: {}, 重试数: {}",
                successCount.get(), failureCount.get(), retryCount.get());
    }
}