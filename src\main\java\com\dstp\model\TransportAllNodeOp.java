package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 运输节点信息 - 所有节点 - 操作节点
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_transport_info_all_node_op")
public class TransportAllNodeOp extends BaseEntity {
    @NotNull(message = "节点信息ID不能为空")
    private Long nodeId; // 关联字段：doc_transport_info_all_node.id
    private String opNode; // 操作节点
    private LocalDateTime opTime; // 操作时间
    private String opLng; // 操作经度
    private String opLat; // 操作维度
    private String record; // 备注
}
