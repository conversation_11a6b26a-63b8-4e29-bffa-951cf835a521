package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;

/**
 * To B 销售商品
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_sale_order_to_b_goods")
public class SaleToBGoods extends BaseEntity {
    @NotNull(message = "2B销售单ID不能为空")
    private Long saleOrderId;
    private String goodsName;
    private String barCode;
    @NotNull(message = "销售数量不能为空")
    private Integer qty;
    private BigDecimal priceTax;
    private BigDecimal price;
    @TableField("category_1")
    private String category1;
    @TableField("category_2")
    private String category2;
}