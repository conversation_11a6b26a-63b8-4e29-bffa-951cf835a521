<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.InternalTransportMapper">
    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="internalTransportMap" type="com.dstp.model.InternalTransport">
        <id property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="entOrderNo" column="ent_order_no"/>
        <result property="sourceSite" column="source_site"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="internalTransportMap">
        SELECT id,ent_id,ent_order_no,source_site
        FROM doc_internal_transport_order
        WHERE type = 20 AND (ent_id,ent_order_no,source_site) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.entId}, #{item.entOrderNo}, #{item.sourceSite})
        </foreach>
    </select>
</mapper>