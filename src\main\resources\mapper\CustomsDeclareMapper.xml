<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.CustomsDeclareMapper">
    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="customsDeclareMap" type="com.dstp.model.CustomsDeclare">
        <id property="id" column="id"/>
        <result property="customerCode" column="customer_code"/>
        <result property="declareNo" column="declare_no"/>
        <result property="synchronousMode" column="synchronous_mode"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="customsDeclareMap">
        SELECT id,customer_code,declare_no,synchronous_mode
        FROM tb_transaction_customs_clearance
        WHERE (customer_code,declare_no) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.customerCode}, #{item.declareNo})
        </foreach>
    </select>

    <update id="updateAttrByIds">
        update tb_transaction_customs_clearance a
        left join tb_declare_customs b on a.declare_code = b.code
        left join tb_declare_customs c on a.entry_exit_code = c.code
        left join tb_supervise_mode d on a.supervise_type = d.code
        left join tb_hs_country e on a.begin_country = e.en_short
        left join tb_customs_clearance_port f on a.begin_port = f.code
        left join tb_hs_country g on a.trade_country = g.en_short
        left join tb_customs_clearance_port h on a.designation_port = h.code
        left join tb_domestic_port i on a.ciq_enty_port_code_name = i.code
        set
        a.custom_master_name = b.name,
        a.ie_port_name = c.name,
        a.supv_mode_cdde_name = d.short_name,
        a.cus_traf_mode_name = (
        case a.transport_mode
        when '0' then	'非保税区'
        when '1' then '监管仓库'
        when '2' then '水路运输'
        when '3' then '铁路运输'
        when '4' then '公路运输'
        when '5' then '航空运输'
        when '6' then '邮件运输'
        when '7' then '保税区'
        when '8' then '保税仓库'
        when '9' then '其他方式运输'
        when 'H' then '边境特殊海关作业区'
        when 'T' then '综合实验区'
        when 'W' then '物流中心'
        when 'X' then '物流园区'
        when 'Y' then '保税港区'
        when 'Z' then '出口加工区'
        when 'L' then '旅客携带'
        when 'G' then '固定设施运输'
        else '其他'
        end
        ),
        a.cus_trade_country_name = e.name,
        a.desp_port_code_name = f.cn_name,
        a.cus_trade_nation_code_name = g.name,
        a.distinate_port_name = h.cn_name,
        a.ports_entry = i.name,
        a.wrap_type_name = (
        case a.wrap_type
        when '00' then '散装'
        when '01' then '裸装'
        when '22' then '纸制或纤维板制盒/箱'
        when '23' then '木制或竹藤等植物性材料制盒/箱'
        when '29' then '其他材料制盒/箱'
        when '32' then '纸制或纤维板制桶'
        when '33' then '木制或竹藤等植物性材料制桶'
        when '39' then '其他材料制桶'
        when '04' then '球状罐类'
        when '06' then '包/袋'
        when '92' then '再生木托'
        when '93' then '天然木托'
        when '98' then '植物性铺垫材料'
        when '99' then '其他包装'
        end
        ),
        a.trans_mode_name = (
        case a.trans_mode
        when '1' then 'CIF'
        when '2' then 'C&amp;F'
        when '3' then 'FOB'
        when '4' then 'C&amp;I'
        when '5' then '市场价'
        when '6' then '垫仓'
        when '7' then 'EXW'
        end
        )
        where a.id in
        <foreach item='id' collection='ids' open='(' separator=',' close=')'>
            #{id}
        </foreach>
    </update>
</mapper>