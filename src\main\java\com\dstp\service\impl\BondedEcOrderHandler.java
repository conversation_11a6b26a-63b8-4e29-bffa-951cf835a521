package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.BondedEcOrderAddress;
import com.dstp.model.BondedEcOrderGoods;
import com.dstp.mapper.BondedEcOrderAddressMapper;
import com.dstp.mapper.BondedEcOrderGoodsMapper;
import com.dstp.mapper.BondedEcOrderMapper;
import com.dstp.model.BondedEcOrder;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class BondedEcOrderHandler extends MessageHandler {
    private final BondedEcOrderMapper bondedEcOrderMapper;
    private final BondedEcOrderGoodsMapper bondedEcOrderGoodsMapper;
    private final BondedEcOrderAddressMapper bondedEcOrderAddressMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode) {
        List<BondedEcOrder> bondedEcOrderList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(bondedEcOrderList)) {
            log.warn("保税电商数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        bondedEcOrderList = distinct(bondedEcOrderList);

        // 1. 新增或更新保税电商订单
        bondedEcOrderMapper.batchUpsert(bondedEcOrderList);

        // 2. 查询已存在的保税电商订单ID
        List<Long> bondedOrderIds = batchGetParentPkId(bondedEcOrderList, bondedEcOrderMapper);

        // 3. 保存新保税电商订单商品
        List<BondedEcOrderGoods> bondedEcOrderGoodsList= batchSetManyChildFkId(bondedEcOrderList,
                BondedEcOrder::getGoodsList, BondedEcOrderGoods::setBondedOrderId);
        bondedEcOrderGoodsMapper.delete(new QueryWrapper<BondedEcOrderGoods>().in("bonded_order_id", bondedOrderIds));
        bondedEcOrderGoodsMapper.batchInsert(bondedEcOrderGoodsList);

        // 4. 保存新保税电商订单地址
        List<BondedEcOrderAddress> bondedEcOrderAddressList= batchSetOneChildFkId(bondedEcOrderList,
                BondedEcOrder::getRecipient, BondedEcOrderAddress::setBondedOrderId);
        bondedEcOrderAddressMapper.delete(new QueryWrapper<BondedEcOrderAddress>().in("bonded_order_id", bondedOrderIds));
        bondedEcOrderAddressMapper.batchInsert(bondedEcOrderAddressList);
    }
}
