package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;

/**
 * 报关清单商品
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_customs_clearance_inventory_goods")
public class CustomsInventoryGoods extends BaseEntity {
    @NotNull(message = "清单ID不能为空")
    private Long inventoryId;  // 关联主表ID
    @JsonProperty("gNum")
    private Integer gNum;
    private String itemCode;
    private String itemName;
    private String goodsCode;
    private String goodsName;
    private String goodsDesc;
    private String goodsModel;  // 规格型号（varchar(510)）
    private String barCode;
    private String country;
    private String currency;
    private BigDecimal quantity;   // 申报数量（DECIMAL(24,5)）
    private BigDecimal quantity1;  // 法定数量
    private String unit;
    private String unit1;
    private BigDecimal price;      // 成交单价
    private BigDecimal amount;     // 成交金额
}
