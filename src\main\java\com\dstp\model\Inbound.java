package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyLongDeserializer;
import com.dstp.deserializer.WeightDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 入库信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_inbound_header")
public class Inbound extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    @NotBlank(message = "入库单号不能为空")
    private String entOrderNo;
    private String busiOrderNo;
    private String serviceOrderNo;
    private String clientOrderNo;
    private String consignerCode;
    private String consignerName;
    private String vehicleNo;
    private LocalDateTime commitTime;
    private String warehouseId;
    private String warehouseName;
    private String bookNo;
    private String inputType;
    @JsonProperty("superviseType")
    private String supervisionType;
    private LocalDateTime actualTime;
    private LocalDateTime requirementTime;
    private String declareCode;
    @JsonProperty("relateBookCode")
    private String relateBookNo;
    private String operateCode;
    private String operateName;
    private String entrustCode;
    private String entrustName;
    private String foreignConsignee;
    private String transportMode;
    private String transportTool;
    private String voyageNo;
    private String billNo;
    private String customsClearanceCode;
    @JsonProperty("customsClearanceName")
    private String customsClearanceCompany;
    private String contractNo;
    private String checkNo;
    private String relateCheckNo;
    private String entryNo;
    private String entryExitCode;
    private String inOutOrderNo;
    private String beginCountry;
    private String beginPort;
    private String tradeCountry;
    private String designationPort;
    private String portsEntry;
    private String transMode;
    private String currency;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long shippingFee;       // 分
    @JsonProperty("premiumFee")
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long premium;           // 分
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long diverseFee;        // 分
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long amount;            // 分
    private BigDecimal declareQty;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal grossWeight; // 克
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal netWeight;   // 克
    private LocalDateTime orderCreateTime;
    private LocalDateTime podTime;
    private LocalDateTime declareTime;
    private LocalDateTime laodTime;
    private LocalDateTime releaseTime;
    @JsonProperty("arrivalTime")
    private LocalDateTime warehouseTime;
    private LocalDateTime tallyTime;
    @JsonProperty("shelfTime")
    private LocalDateTime putawayTime;
    private String logisticsNo;
    private String brand;
    private Long sourceEntId;
    private Long serverEntId;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    private String linkId;
    // 一对多关联关系
    @TableField(exist = false, select = false)
    private List<InboundAttachment> attachmentList;
    @TableField(exist = false, select = false)
    private List<InboundContainer> containerList;
    @TableField(exist = false, select = false)
    private List<InboundGoods> goodsList;
    @TableField(exist = false, select = false)
    private List<InboundVehicle> vehicleList;

    public String getUniqueKeyString() {
        if (entId == null || entOrderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + entOrderNo + ":" + sourceSite;
    }
}