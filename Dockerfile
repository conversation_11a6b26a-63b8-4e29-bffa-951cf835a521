# 使用轻量级 OpenJDK 17 镜像（Alpine Linux 为基础）
FROM openjdk:17-jdk-alpine

# 设置时区为上海（+8时区）
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk add --no-cache tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 设置容器内工作目录（可选，但推荐）
WORKDIR /opt

# 将构建的 Spring Boot JAR 文件复制到镜像中
COPY target/*.jar app.jar

# 优化 JVM 参数（缩短启动时间，内存管理）
ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-XX:+UseContainerSupport", "-jar", "/opt/app.jar"]

# 声明容器监听的端口（Spring Boot 默认 8080）
EXPOSE 8080