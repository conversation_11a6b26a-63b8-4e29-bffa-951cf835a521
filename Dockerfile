# 使用轻量级 OpenJDK 17 镜像（Alpine Linux 为基础）
FROM openjdk:17-jdk-alpine

# 设置时区为上海（+8时区）
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk add --no-cache tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 设置JVM参数，针对数据处理服务优化
ENV JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication"

WORKDIR /opt
COPY target/*.jar app.jar

ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /opt/app.jar"]

EXPOSE 8080