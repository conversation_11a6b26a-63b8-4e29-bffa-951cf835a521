package com.dstp.service;

import com.dstp.mapper.CommonGoodsMapper;
import com.dstp.model.CommonGoodsAttr;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
@RequiredArgsConstructor
@CacheConfig(cacheNames = "commonGoodsCache", cacheManager = "commonGoodsCacheManager")
public class CommonGoodsService extends BaseCacheService<CommonGoodsAttr, String> {
    private final CommonGoodsMapper commonGoodsMapper;
    private final CacheManager commonGoodsCacheManager;

    /**
     * 根据条形码获取标准商品信息
     * 使用缓存提高查询性能
     *
     * @param barcode 条形码
     * @return 标准商品信息
     */
    @Cacheable(key = "#barcode", condition = "#barcode != null", unless = "#result == null")
    public CommonGoodsAttr getGoodsAttrByBarcode(String barcode) {
        if (StringUtils.isBlank(barcode)) {
            return null;
        }

        String standardBarcode = standardizingBarcode(barcode);
        log.debug("缓存未命中，从数据库查询标准商品信息: 条形码={}", barcode);
        return commonGoodsMapper.selectGoodsAttrByBarcode(standardBarcode);
    }

    /**
     * 根据条形码表获取标准商品信息映射
     * 使用缓存提高查询性能
     * 优化批量查询逻辑，避免逐个查询
     *
     * @param barcodes 条形码列表
     * @return 标准商品信息映射
     */
    public Map<String, CommonGoodsAttr> getGoodsAttrByBarcodeList(List<String> barcodes) {
        if (CollectionUtils.isEmpty(barcodes)) {
            return new HashMap<>();
        }

        // 去重处理并标准化条形码
        List<String> distinctBarcodes = barcodes.stream()
                .filter(StringUtils::isNotBlank)
                .map(this::standardizingBarcode)
                .distinct()
                .toList();

        // 使用基类的批量查询方法
        return batchGet(distinctBarcodes);
    }

    // 标准化商品barcode
    private String standardizingBarcode(String barcode) {
        // 正则1：匹配11到14位数字或9开头的7位数字
        Pattern pattern1 = Pattern.compile("^\\d{11,14}$|^9\\d{7}$");
        // 正则2：匹配12到14位数字，后面跟着非数字
        Pattern pattern2 = Pattern.compile("^\\d{12,14}\\D$");

        // 筛选并标准化barcode
        if (pattern1.matcher(barcode).matches()) {
            // 如果符合第一个正则，补全为14位数字
            return barcode.length() < 14 ? String.format("%014d", Long.parseLong(barcode)) : barcode;
        } else if (pattern2.matcher(barcode).matches()) {
            // 如果符合第二个正则，提取12到14位数字
            Matcher matcher = Pattern.compile("^\\d{12,14}").matcher(barcode);
            // 如果匹配成功，返回提取的数字，补全为14位数字
            if (matcher.find()) {
                String numberPart = matcher.group();
                return numberPart.length() < 14 ? String.format("%014d", Long.parseLong(numberPart)) : numberPart;
            }
        }
        return barcode;
    }

    @Override
    protected CacheManager getCacheManager() {
        return commonGoodsCacheManager;
    }

    @Override
    protected String getCacheName() {
        return "commonGoodsCache";
    }

    @Override
    protected String getCacheStatsPrefix() {
        return "标准商品";
    }

    @Override
    protected List<CommonGoodsAttr> batchQueryFromDb(List<String> keys) {
        if (keys.isEmpty()) {
            return new ArrayList<>();
        }
        return commonGoodsMapper.selectGoodsAttrByBarcodeList(keys);
    }

    @Override
    protected String getKey(CommonGoodsAttr obj) {
        return obj.getBarCode();
    }
}
