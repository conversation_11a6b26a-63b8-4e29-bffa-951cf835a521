package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 核注单
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_check_order")
public class Check extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    private String entOrderNo;
    private String busiOrderNo;
    private String serviceOrderNo;
    private String contractNo;
    private String declareCode;
    private String entryExitCode;
    private String enterpriseCode;
    private String enterpriseName;
    private String transportMode;
    private String superviseType;
    private String customsClearanceCode;
    private String customsClearanceName;
    @NotBlank(message = "核注单号不能为空")
    private String checkNo;
    private String passportNo;
    private String entryNo;
    private String bondInvtNo;
    private String bondInvtType;
    private String beginCountry;
    private String ieFlag;
    private String dclcusFlag;
    private LocalDateTime formalVrfdedTime;
    private LocalDateTime orderCreateTime;
    private LocalDateTime declareTime;
    private LocalDateTime releaseTime;
    private LocalDateTime inspectionTime;
    private String mtpckEndprdMarkcd;
    private String inboundOrderNo;
    private String outboundOrderNo;
    private String bizopEtpsno;
    private String bizopEtpsNm;
    private String booksNo;
    private Long sourceEntId;
    private Long serverEntId;
    private String synchronousMode;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    private String linkId;
    // 一对多关联实体
    @TableField(exist = false, select = false)
    @JsonProperty("orderList")
    private List<CheckEntity> entityList;
    // 一对多关联商品
    @TableField(exist = false, select = false)
    private List<CheckGoods> goodsList;

    @Override
    public String getUniqueKeyString() {
        if (entId == null || checkNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + checkNo + ":" + sourceSite;
    }
}