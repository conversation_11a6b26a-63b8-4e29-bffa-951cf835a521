package com.dstp.service;

import com.dstp.mapper.ExchangeRateMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 汇率服务
 * 提供汇率信息查询和缓存管理
 */
@Service
@Slf4j
@RequiredArgsConstructor
@CacheConfig(cacheNames = "exchangeRateCache", cacheManager = "exchangeRateCacheManager")
public class ExchangeRateService {

    private final ExchangeRateMapper exchangeRateMapper;

    /**
     * 根据币种和汇率月份获取汇率
     * @param currency 币种
     * @param rateMonth 汇率月份
     * @return 汇率
     */
    @Cacheable(key = "#currency + '-' + #rateMonth", condition = "#currency != null && #rateMonth != null", unless = "#result == null") // 仅当 currency和rateMonth不为空时使用缓存
    public BigDecimal getExchangeRateByCurrencyAndYM(String currency, String rateMonth) {
        // 如果币种或汇率月份为空，则返回 1（人民币兑人民币）
        if (StringUtils.isAnyBlank(currency, rateMonth)) {
            return new BigDecimal(1);
        }
        log.debug("缓存未命中，从数据库查询汇率信息: 币种={}，汇率月份={}", currency, rateMonth);
        BigDecimal exchangeRate = exchangeRateMapper.getExchangeRateByCurrencyAndYM(currency, rateMonth);

        // 如果查询结果为空，则返回 1（人民币兑人民币）
        if (exchangeRate == null) {
            return new BigDecimal(1);
        }
        return exchangeRateMapper.getExchangeRateByCurrencyAndYM(currency, rateMonth);
    }
}
