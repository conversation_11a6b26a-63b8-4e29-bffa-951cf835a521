package com.dstp.mapper.injector.methods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlInjectionUtils;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlScriptUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.keygen.Jdbc3KeyGenerator;
import org.apache.ibatis.executor.keygen.KeyGenerator;
import org.apache.ibatis.executor.keygen.NoKeyGenerator;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

import java.util.List;
import java.util.function.Predicate;

@Slf4j
public class BatchInsert extends AbstractMethod {
    private Predicate<TableFieldInfo> predicate;

    public BatchInsert() {
        super(SqlMethod.BATCH_INSERT.getMethod());
    }

    public BatchInsert(Predicate<TableFieldInfo> predicate) {
        super(SqlMethod.BATCH_INSERT.getMethod());
        this.predicate = predicate;
    }

    public BatchInsert(String name, Predicate<TableFieldInfo> predicate) {
        super(name);
        this.predicate = predicate;
    }

    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        KeyGenerator keyGenerator = NoKeyGenerator.INSTANCE;
        SqlMethod sqlMethod = SqlMethod.BATCH_INSERT;

        List<TableFieldInfo> fieldList = tableInfo.getFieldList();

        // 生成列脚本（无动态条件）
        String columnScript = SqlScriptUtils.convertTrim(
                this.filterTableFieldInfo(fieldList, predicate, TableFieldInfo::getInsertSqlColumn, EMPTY),
                LEFT_BRACKET, RIGHT_BRACKET, null, COMMA);

        // 生成单行值的trim脚本（使用item前缀）,并用foreach包裹
        String valuesScript = SqlScriptUtils.convertForeach(
                SqlScriptUtils.convertTrim(
                        this.filterTableFieldInfo(fieldList, predicate, i -> i.getInsertSqlProperty("item."), EMPTY),
                        LEFT_BRACKET, RIGHT_BRACKET, null, COMMA
                ), "list", null,"item", COMMA);

        String keyProperty = null;
        String keyColumn = null;
        if (tableInfo.havePK()) {
            if (tableInfo.getIdType() == IdType.AUTO) {
                keyGenerator = Jdbc3KeyGenerator.INSTANCE;
                keyProperty = tableInfo.getKeyProperty();
                keyColumn = SqlInjectionUtils.removeEscapeCharacter(tableInfo.getKeyColumn());
            } else if (null != tableInfo.getKeySequence()) {
                keyGenerator = TableInfoHelper.genKeyGenerator(this.methodName, tableInfo, this.builderAssistant);
                keyProperty = tableInfo.getKeyProperty();
                keyColumn = tableInfo.getKeyColumn();
            }
        }

        String sql = String.format(sqlMethod.getSql(), tableInfo.getTableName(), columnScript, valuesScript);
        SqlSource sqlSource = super.createSqlSource(this.configuration, sql, modelClass);
        return this.addInsertMappedStatement(mapperClass, modelClass, this.methodName, sqlSource, keyGenerator, keyProperty, keyColumn);
    }

    public BatchInsert setPredicate(final Predicate<TableFieldInfo> predicate) {
        this.predicate = predicate;
        return this;
    }
}
