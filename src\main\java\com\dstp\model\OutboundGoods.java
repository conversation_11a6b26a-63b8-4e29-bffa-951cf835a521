package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyLongDeserializer;
import com.dstp.deserializer.WeightDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 出库商品
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_outbound_goods")
public class OutboundGoods extends BaseEntity {
    @NotNull(message = "出库单ID不能为空")
    private Long outboundOrderId;
    @JsonProperty("gNum")
    private String gNum;
    private String bookItemNo;
    @JsonProperty("goodsNo")
    private String goodNo;
    @JsonProperty("barCode")
    private String barcode;
    private String entGoodsNo;
    @JsonProperty("goodsName")
    private String goodName;
    private String entGoodsName;
    @JsonProperty("hsCode")
    private String hscode;
    private String specification;
    @JsonProperty("specDescription")
    private String specificationDesc;
    private String orgCountry;
    private String destinationCountry;
    private String natureExemption;
    @JsonProperty("declareUnit")
    private String unit;
    @JsonProperty("declareQty")
    private BigDecimal qty;
    private String storageUnit;
    private BigDecimal enterpriseQty;
    private BigDecimal declareVal;
    private BigDecimal planQty;
    private BigDecimal actualQty;
    private String firstLegalUnit;
    private BigDecimal firstLegalQty;
    private String secondLegalUnit;
    private BigDecimal secondLegalQty;
    @JsonProperty("declareCurrency")
    private String currency;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long declareUnitPrice;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long declareTotalPrice;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal grossWeight;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal netWeight;
    private String lotNo;
    private LocalDateTime productDate;
    private LocalDateTime expiryDate;
    private String traceSourceCode;
    private String notes;
}
