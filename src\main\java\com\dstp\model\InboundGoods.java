package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyLongDeserializer;
import com.dstp.deserializer.WeightDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 入库商品
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_inbound_goods")
public class InboundGoods extends BaseEntity {
    @NotNull(message = "入库单ID不能为空")
    private Long inboundOrderId;
    @JsonProperty("gNum")
    private String gNum;
    private String bookItemNo;
    private String goodsNo;
    @JsonProperty("barCode")
    private String barcode;
    private String entGoodsNo;
    private String goodsName;
    private String entGoodsName;
    @JsonProperty("hsCode")
    private String hscode;
    private String specification;
    @JsonProperty("specDescription")
    private String specificationDesc;
    private String orgCountry;
    private String destinationCountry;
    private String natureExemption;
    private String declareUnit;
    private BigDecimal declareQty;
    private String storageUnit;
    private BigDecimal enterpriseQty;
    private String firstLegalUnit;
    private BigDecimal firstLegalQty;
    private String secondLegalUnit;
    private BigDecimal secondLegalQty;
    private String declareCurrency;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long declareUnitPrice;  // 分
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long declareTotalPrice; // 分
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal grossWeight; // 克
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal netWeight;   // 克
    private String lotNo;
    private LocalDateTime productDate;
    private LocalDateTime expiryDate;
    private String traceSourceCode;
    private String notes;
}
