package com.dstp.mapper.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Properties;

/**
 * 空参数拦截器，拦截写入参数为空自定义SQL（batchInser、batchUpsert、upsert）方法，跳过SQL执行
 */
@Slf4j
@Component
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
public class EmptyParamInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];
        Object parameter = args[1];

        String msId = ms.getId();
        // 检查方法名是否为upsert或batchUpsert
        if (msId.endsWith(".upsert")) {
            // 单个对象为null，直接返回false
            if (parameter == null) {
                log.warn("upsert方法参数为null，跳过SQL执行");
                return false;
            }
        } else if (msId.endsWith(".batchUpsert") || msId.endsWith(".batchInsert")) {
            // 批量操作，检查列表是否为空
            if (parameter == null) {
                log.warn("批量操作方法参数为null，跳过SQL执行");
                return 0;
            }

            // 获取list参数
            List<?> list = null;

            // 如果参数直接是List
            if (parameter instanceof List) {
                list = (List<?>) parameter;
            }
            // 如果参数是Map，尝试获取list属性
            else if (parameter instanceof java.util.Map<?, ?> paramMap) {
                if (paramMap.containsKey("list")) {
                    Object listObj = paramMap.get("list");
                    if (listObj instanceof List) {
                        list = (List<?>) listObj;
                    }
                }
            }
            // 其他情况，尝试通过反射获取list属性
            else {
                try {
                    Object listObj = parameter.getClass().getMethod("getList").invoke(parameter);
                    if (listObj instanceof List) {
                        list = (List<?>) listObj;
                    }
                } catch (Exception e) {
                    // 无法获取list，继续执行
                    log.debug("无法从参数中获取list属性，继续执行SQL");
                }
            }

            // 检查列表是否为空
            if (list != null && list.isEmpty()) {
                log.warn("批量操作方法参数列表为空，跳过SQL执行");
                return 0;
            }
        }

        // 正常执行
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 不需要额外属性
    }
}
