package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyLongDeserializer;
import com.dstp.deserializer.WeightDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商贸商品 + 物流商品
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_goods")
public class Goods extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    private String goodsNo;
    @NotNull(message = "商品货号不能为空")
    private String entGoodsNo;
    private String entGoodsName;
    private String goodsName;
    private String goodsNameEn;
    private String hsCode;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long price;
    private String parentBrand; //
    private String brand;
    private String firstCategory;
    private String secondCategory;
    private String thirdCategory;
    private String brandEn;
    private String orgCountry;
    private String specification;
    private String specDescription;
    private String declareUnit;
    private String storageUnit;
    private BigDecimal declareVal;
    private String firstLegalUnit;
    private String secondLegalUnit;
    private String produceCompany;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal grossWeight;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal netWeight;
    private String barCode;
    private String composition;
    private String cargoFunction;
    private String cargoPurpose;
    private Integer additiveFlag;
    private Integer additiveBeyondScope;
    private String manufacturerAddr;
    private Integer transgenicFlag;
    private String packageType;
    private String picUrl;
    private String currency;
    private Long sourceEntId;
    private Long serverEntId;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    private String linkId;
    @TableField(exist = false, select = false)
    private List<GoodsCate> goodsCateList;

    public String getUniqueKeyString() {
        if (entId == null || entGoodsNo == null) {
            return null;
        }
        return entId + ":" + entGoodsNo;
    }

    public Goods(Long entId, String entGoodsNo) {
        this.entId = entId;
        this.entGoodsNo = entGoodsNo;
    }
}
