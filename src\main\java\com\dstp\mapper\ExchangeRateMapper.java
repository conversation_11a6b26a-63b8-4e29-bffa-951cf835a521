package com.dstp.mapper;

import com.dstp.model.ExchangeRate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

@Mapper
public interface ExchangeRateMapper extends MyBaseMapper<ExchangeRate> {

    @Select("""
        SELECT reference_rate
        FROM (
            SELECT
                reference_rate,
                DATE_ADD(published_time, INTERVAL 1 DAY) AS published_time,
                DATE_FORMAT(DATE_ADD(published_time, INTERVAL 1 DAY), '%Y-%m') AS rate_month
            FROM currency_exchange.exchange_rate
            WHERE from_code = #{currency}
              AND to_code = 'CNY'
              AND data_source = 'WGJ'
              AND DAY(DATE_ADD(published_time, INTERVAL 1 DAY)) IN (1, 2)
        ) t
        WHERE t.rate_month = #{rateMonth}
        ORDER BY published_time DESC
        LIMIT 1
    """)
    BigDecimal getExchangeRateByCurrencyAndYM(@Param("currency") String currency, @Param("rateMonth") String rateMonth);
}
