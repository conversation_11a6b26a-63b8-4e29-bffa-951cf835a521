package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.FinancingRefundDetail;
import com.dstp.mapper.FinancingRefundDetailMapper;
import com.dstp.mapper.FinancingRefundMapper;
import com.dstp.model.FinancingRefund;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class FinancingRefundHandler extends MessageHandler {
    private final FinancingRefundMapper financingRefundMapper;
    private final FinancingRefundDetailMapper financingRefundDetailMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<FinancingRefund> financingRefundList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(financingRefundList)) {
            log.warn("融资还款数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        financingRefundList = distinct(financingRefundList);

        // 1. 新增或更新还款信息
        financingRefundMapper.batchUpsert(financingRefundList);

        // 2. 查询已存在的还款信息ID
        List<Long> refundIds = batchGetParentPkId(financingRefundList, financingRefundMapper);

        // 3. 保存新还款信息明细
        List<FinancingRefundDetail> financingRefundDetailList = batchSetManyChildFkId(financingRefundList,
                FinancingRefund::getRefundList, FinancingRefundDetail::setRefundId);
        // 删除旧还款信息明细
        financingRefundDetailMapper.delete(new QueryWrapper<FinancingRefundDetail>().in("refund_id", refundIds));
        // 保存新还款信息明细
        financingRefundDetailMapper.batchInsert(financingRefundDetailList);
    }
}
