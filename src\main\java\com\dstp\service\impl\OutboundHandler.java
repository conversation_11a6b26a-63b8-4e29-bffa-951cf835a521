package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.*;
import com.dstp.mapper.OutboundGoodsMapper;
import com.dstp.mapper.OutboundMapper;
import com.dstp.mapper.OutboundSplintMapper;
import com.dstp.mapper.OutboundVehicleMapper;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class OutboundHandler extends MessageHandler {
    private final OutboundMapper outboundMapper;
    private final OutboundGoodsMapper outboundGoodsMapper;
    private final OutboundSplintMapper outboundSplintMapper;
    private final OutboundVehicleMapper outboundVehicleMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<Outbound> outboundList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(outboundList)) {
            log.warn("出库数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        outboundList = distinct(outboundList);

        // 1. 新增或更新出库信息
        outboundMapper.batchUpsert(outboundList);

        // 2. 查询已存在的出库信息ID
        List<Long> outboundId = batchGetParentPkId(outboundList, outboundMapper);

        // 3. 保存新出库商品信息
        List<OutboundGoods> outboundGoodsList = batchSetManyChildFkId(outboundList,
                Outbound::getGoodsList, OutboundGoods::setOutboundOrderId);
        outboundGoodsMapper.delete(new QueryWrapper<OutboundGoods>().in("outbound_order_id", outboundId));
        outboundGoodsMapper.batchInsert(outboundGoodsList);

        // 4. 保存新出库托盘信息
        List<OutboundSplint> outboundSplintList = batchSetManyChildFkId(outboundList,
                Outbound::getSplintList, OutboundSplint::setOutboundOrderId);
        outboundSplintMapper.delete(new QueryWrapper<OutboundSplint>().in("outbound_order_id", outboundId));
        outboundSplintMapper.batchInsert(outboundSplintList);

        // 5. 保存新出库车辆信息
        List<OutboundVehicle> outboundVehicleList = batchSetManyChildFkId(outboundList,
                Outbound::getVehicleList, OutboundVehicle::setOutboundOrderId);
        outboundVehicleMapper.delete(new QueryWrapper<OutboundVehicle>().in("outbound_order_id", outboundId));
        outboundVehicleMapper.batchInsert(outboundVehicleList);
    }
}
