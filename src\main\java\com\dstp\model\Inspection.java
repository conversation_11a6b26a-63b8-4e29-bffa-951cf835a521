package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.List;

/**
 * 检务信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_inspection_info")
public class Inspection extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    private String busiOrderNo;
    private String serviceOrderNo;
    @NotBlank(message = "企业订单号不能为空")
    private String entOrderNo;
    private String clientOrderNo;
    private String ieFlag;
    private String superviseType;
    private String declareCategory;
    private Long sourceEntId;
    private Long serverEntId;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    private String linkId;
    // 一对多关联单证信息
    @TableField(exist = false, select = false)
    private List<InspectionLicence> licenceList;

    public String getUniqueKeyString() {
        if (entId == null || entOrderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + entOrderNo + ":" + sourceSite;
    }
}