<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.TransportMapper">
    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="transportMap" type="com.dstp.model.Transport">
        <id property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="statementNum" column="statement_num"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="transportMap">
        SELECT id,ent_id,statement_num
        FROM doc_transport_info
        WHERE (ent_id,statement_num) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.entId}, #{item.statementNum})
        </foreach>
    </select>
</mapper>