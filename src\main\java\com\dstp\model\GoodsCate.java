package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 商品证书
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_goods_cate")
public class GoodsCate extends BaseEntity {
    @JsonProperty("gNum")
    private String gNum;  // 序号
    @NotNull(message = "商品ID不能为空")
    private Long goodsId; // 关联主表ID
    private String certName;
    private String certUrl;
}
