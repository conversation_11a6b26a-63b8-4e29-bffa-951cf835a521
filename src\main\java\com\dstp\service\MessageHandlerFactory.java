package com.dstp.service;

import com.dstp.service.impl.*;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class MessageHandlerFactory {
    private final Map<String, MessageHandler> handlers = new HashMap<>();

    private final BondedEcLogisticsHandler bondedEcLogisticsService;
    private final BondedEcOrderHandler bondedEcOrderService;
    private final BookingSpaceHandler bookingSpaceService;
    private final CheckHandler checkService;
    private final CreditPlanHandler creditPlanService;
    private final CustomsInventoryHandler customsInventoryService;
    private final CustomsDeclareHandler customsDeclareService;
    private final FinancingApplyHandler financingApplyService;
    private final FinancingRefundHandler financingRefundService;
    private final GoodsHandler goodsHandler;
    private final ImportExportLogisticsHandler importExportLogisticsService;
    private final InboundHandler inboundService;
    private final InspectionHandler inspectionService;
    private final InternalTransportHandler internalTransportService;
    private final InventoryHandler inventoryHandler;
    private final InventorySnapshotHandler inventorySnapshotService;
    private final OutboundHandler outboundService;
    private final PodHandler podService;
    private final PurchaseHandler purchaseService;
    private final SaleToBHandler saleToBService;
    private final SaleToCHandler saleToCHandler;
    private final SettlementInfoHandler settlementInfoService;
    private final StorageVoucherHandler storageVoucherService;
    private final TallyHandler tallyService;
    private final TransportHandler transportService;

    @PostConstruct
    public void init() {
        handlers.put("dxc_financial_credit_info", creditPlanService);
        handlers.put("dxc_financial_info", financingApplyService);
        handlers.put("dxc_financial_repayment_info", financingRefundService);
        handlers.put("dxc_info_inventory_query", inventoryHandler);
        handlers.put("dxc_logistics_bonded_ec_logistics", bondedEcLogisticsService);
        handlers.put("dxc_logistics_bonded_ec_order", bondedEcOrderService);
        handlers.put("dxc_logistics_booking_space", bookingSpaceService);
        handlers.put("dxc_logistics_check_order", checkService);
        handlers.put("dxc_logistics_customs_clearance", customsDeclareService);
        handlers.put("dxc_logistics_goods", goodsHandler);
        handlers.put("dxc_logistics_inbound_order", inboundService);
        handlers.put("dxc_logistics_inspection_info", inspectionService);
        handlers.put("dxc_logistics_inventory_information", customsInventoryService);
        handlers.put("dxc_logistics_inventory_snapshot", inventorySnapshotService);
        handlers.put("dxc_logistics_outbound_order", outboundService);
        handlers.put("dxc_logistics_pod_order", podService);
        handlers.put("dxc_logistics_storage_voucher", storageVoucherService);
        handlers.put("dxc_logistics_tally_order", tallyService);
        handlers.put("dxc_logistics_transport", internalTransportService);
        handlers.put("dxc_newly_tech_transport_info", transportService);
        handlers.put("dxc_trade_purchase", purchaseService);
        handlers.put("dxc_trade_sale_tob", saleToBService);
        handlers.put("dxc_trade_sale_toc", saleToCHandler);
        handlers.put("dxc_trade_settlement_info", settlementInfoService);
        handlers.put("dxc_trade_synchronize_goods", goodsHandler);
        handlers.put("import_export_logistics_info", importExportLogisticsService);
    }

    public MessageHandler getHandler(String topic) {
        return handlers.get(topic);
    }
}
