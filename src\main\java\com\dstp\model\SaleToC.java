package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyLongDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * To C 销售
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_sale_order_to_c")
public class SaleToC extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    @NotBlank(message = "企业订单编号不能为空")
    private String entOrderNo;
    private String platformOrderNo;
    private String declareCode;
    private String clearanceType;
    @NotNull(message = "订单时间不能为空")
    private LocalDateTime orderTime;
    private LocalDateTime deliveryTime;
    private LocalDateTime declareTime;
    private LocalDateTime releaseTime;
    private String warehouse;
    private String logisticsName;
    private String wayBillNo;
    private Integer orderType;
    private String agentCode;
    private String agentName;
    private String cbepcomName;
    private String shop;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long shippingFee;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long taxFee;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long totalAmount;
    private String currency;
    private String orderStatus;
    private Long sourceEntId;
    private Long serverEntId;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    private String linkId;
    private Boolean isCheck;
    @TableField(exist = false, select = false)
    private SaleToCAddress recipient;
    @JsonProperty("goodList")
    @TableField(exist = false, select = false)
    private List<SaleToCGoods> goodsList;
    @TableField(exist = false, select = false)
    private List<SaleToCPurchase> purchaseList;

    public String getUniqueKeyString() {
        if (entId == null || entOrderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + entOrderNo + ":" + sourceSite;
    }
}