package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 入库货柜
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_inbound_container")
public class InboundContainer extends BaseEntity {
    @NotNull(message = "入库单ID不能为空")
    private Long inboundOrderId;
    @JsonProperty("gNum")
    private String gNum;
    private String containerNo;
    private String containerModel;
    private String sealNo;
    private Integer cabinets;  // 拼柜标识
    private String notes;
}