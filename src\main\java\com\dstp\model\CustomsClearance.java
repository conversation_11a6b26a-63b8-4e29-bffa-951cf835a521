package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.WeightDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报关单
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_customs_clearance_order")
public class CustomsClearance extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    @NotBlank(message = "订单号/电商平台订单号不能为空")
    private String entOrderNo;
    private String busiOrderNo;
    private String serviceOrderNo;
    private String portBonded;
    private String contractNo;
    private String declareCode;
    private String entryExitCode;
    private String foreignConsigneeNo;
    private String foreignConsignee;
    private String domesticConsigneeNo;
    private String domesticConsignee;
    private String consumUnitName;
    private String consumUnitNo;
    private String transportMode;
    private String superviseType;
    private String transportTool;
    private String voyageNo;
    private String transportBlno;
    private String blno;
    private String customsClearanceCode;
    private String customsClearanceName;
    private String overseasCustomerName;
    private String checkNo;
    private String passportNo;
    private String entryNo;
    private String beginCountry;
    private String beginPort;
    private String tradeCountry;
    private String designationPort;
    private String portsEntry;
    private String ieFlag;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal grossWeight;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal netWeight;
    private String wrapType;
    private String goodsPlace;
    @NotNull(message = "接单时间不能为空")
    private LocalDateTime orderCreateTime;
    private LocalDateTime declareTime;
    private LocalDateTime releaseTime;
    private String inboundOrderNo;
    private String outboundOrderNo;
    private String transMode;
    private Integer packNo;
    private String declareCategory;
    private Integer isSwitch;
    private Integer isLabor;
    private LocalDateTime inspectionTime;
    private Long sourceEntId;
    private Long serverEntId;
    private String synchronousMode;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    private String linkId;
    // 一对多关联商品信息
    @TableField(exist = false, select = false)
    private List<CustomsClearanceGoods> goodsList;

    @Override
    public String getUniqueKeyString() {
        if (entId == null || entOrderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + entOrderNo + ":" + sourceSite;
    }
}