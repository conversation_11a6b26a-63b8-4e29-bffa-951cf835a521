package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.*;
import com.dstp.mapper.*;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class TransportHandler extends MessageHandler {

    private final TransportMapper transportMapper;
    private final TransportNodeMapper transportNodeMapper;
    private final TransportNodeOpMapper transportNodeOpMapper;
    private final TransportAllNodeMapper transportAllNodeMapper;
    private final TransportAllNodeOpMapper transportAllNodeOpMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<Transport> transportList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(transportList)) {
            log.warn("运输节点数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        transportList = distinct(transportList);

        // 1. 新增或更新运输信息
        transportMapper.batchUpsert(transportList);

        // 2. 查询已存在的运输信息ID
        List<Long> transportIds = batchGetParentPkId(transportList, transportMapper);

        // 3. 保存新运输节点信息
        List<TransportNode> transportNodeList = batchSetManyChildFkId(transportList,
                Transport::getNode, TransportNode::setTransportId);
        List<Long> transportNodeIds = transportNodeMapper.selectObjs(new QueryWrapper<TransportNode>().in("transport_id", transportIds).select("id"));
        transportNodeMapper.delete(new QueryWrapper<TransportNode>().in("transport_id", transportIds));
        transportNodeMapper.batchInsert(transportNodeList);

        // 4. 保存新运输节点-操作信息
        List<TransportNodeOp> transportNodeOpList = batchSetManyChildFkId(transportNodeList,
                TransportNode::getOperation, TransportNodeOp::setNodeId);
        if (transportNodeIds.size() > 0) transportNodeOpMapper.delete(new QueryWrapper<TransportNodeOp>().in("node_id", transportNodeIds));
        transportNodeOpMapper.batchInsert(transportNodeOpList);

        // 5. 保存新运输所有节点信息
        List<TransportAllNode> transportAllNodeList = batchSetManyChildFkId(transportList,
                Transport::getAllNode, TransportAllNode::setTransportId);
        List<Long> transportAllNodeIds = transportAllNodeMapper.selectObjs(new QueryWrapper<TransportAllNode>().in("transport_id", transportIds).select("id"));
        transportAllNodeMapper.delete(new QueryWrapper<TransportAllNode>().in("transport_id", transportIds));
        transportAllNodeMapper.batchInsert(transportAllNodeList);

        // 6. 保存新运输所有节点-操作信息
        List<TransportAllNodeOp> transportAllNodeOpList = batchSetManyChildFkId(transportAllNodeList,
                TransportAllNode::getOperation, TransportAllNodeOp::setNodeId);
        if (transportAllNodeIds.size() > 0) transportAllNodeOpMapper.delete(new QueryWrapper<TransportAllNodeOp>().in("node_id", transportAllNodeIds));
        transportAllNodeOpMapper.batchInsert(transportAllNodeOpList);
    }
}
