
package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.mapper.CheckEntityMapper;
import com.dstp.mapper.CheckGoodsMapper;
import com.dstp.mapper.CheckMapper;
import com.dstp.model.*;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class CheckHandler extends MessageHandler {

    private final CheckMapper checkMapper;
    private final CheckEntityMapper checkEntityMapper;
    private final CheckGoodsMapper checkGoodsMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode)  {
        List<Check> checkList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(checkList)) {
            log.warn("核注数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        checkList = distinct(checkList);

        // 1. 新增或更新核注单
        checkList.forEach(check -> {
            if (StringUtils.isBlank(check.getSynchronousMode())) check.setSynchronousMode("1");
        });
        checkMapper.batchUpsert(checkList);

        // 2. 查询已存在的核注单ID
        List<Long> checkIds = batchGetParentPkId(checkList, checkMapper);

        // 3. 保存新核注清单
        List<CheckEntity> checkEntityList = batchSetManyChildFkId(checkList,
                Check::getEntityList, CheckEntity::setOrderId);
        checkEntityMapper.delete(new QueryWrapper<CheckEntity>().in("order_id", checkIds));
        checkEntityMapper.batchInsert(checkEntityList);

        // 4. 保存新核注商品
        List<CheckGoods> checkGoodsList = batchSetManyChildFkId(checkList,
                Check::getGoodsList, CheckGoods::setOrderId);
        checkGoodsMapper.delete(new QueryWrapper<CheckGoods>().in("order_id", checkIds));
        checkGoodsMapper.batchInsert(checkGoodsList);
    }
}