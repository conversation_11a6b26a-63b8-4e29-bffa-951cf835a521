<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.PurchaseMapper">
    <!-- 单条插入或更新 -->
    <insert id="upsertByUnique" parameterType="com.dstp.model.Purchase" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO doc_purchase_order(
        ent_id,ent_order_no,order_time,contract_no,po_order_no,merchant,financing_order_no,purchase_qty,
        purchase_amount,currency,pay_time,wms_order_no,inbound_time,order_status,source_ent_id,server_ent_id,
        created_at,created_by,modified_at,modified_by,source_site,data_source)
        VALUES(
        #{entId},#{entOrderNo},#{orderTime},#{contractNo},#{poOrderNo},#{merchant},#{financingOrderNo},#{purchaseQty},
        #{purchaseAmount},#{currency},#{payTime},#{wmsOrderNo},#{inboundTime},#{orderStatus},#{sourceEntId},#{serverEntId},
        #{createdAt},#{createdBy},#{modifiedAt},#{modifiedBy},#{sourceSite},#{dataSource})
        ON DUPLICATE KEY UPDATE
        id = LAST_INSERT_ID(id),
        order_time = values(order_time),
        contract_no = values(contract_no),
        po_order_no = values(po_order_no),
        merchant = values(merchant),
        financing_order_no = values(financing_order_no),
        purchase_qty = values(purchase_qty),
        purchase_amount = values(purchase_amount),
        currency = values(currency),
        pay_time = values(pay_time),
        wms_order_no = values(wms_order_no),
        inbound_time = values(inbound_time),
        order_status = values(order_status),
        source_ent_id = values(source_ent_id),
        server_ent_id = values(server_ent_id),
        modified_at = VALUES(modified_at),
        modified_by = VALUES(modified_by),
        data_source = VALUES(data_source)
    </insert>

    <!-- 单条插入或更新 -->
    <insert id="batchUpsertByUnique" parameterType="java.util.List">
        INSERT INTO doc_purchase_order(
        ent_id,ent_order_no,order_time,contract_no,po_order_no,merchant,financing_order_no,purchase_qty,
        purchase_amount,currency,pay_time,wms_order_no,inbound_time,order_status,source_ent_id,server_ent_id,
        created_at,created_by,modified_at,modified_by,source_site,data_source)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.entId},#{item.entOrderNo},#{item.orderTime},#{item.contractNo},#{item.poOrderNo},#{item.merchant},#{item.financingOrderNo},#{item.purchaseQty},
            #{item.purchaseAmount},#{item.currency},#{item.payTime},#{item.wmsOrderNo},#{item.inboundTime},#{item.orderStatus},#{item.sourceEntId},#{item.serverEntId},
            #{item.createdAt},#{item.createdBy},#{item.modifiedAt},#{item.modifiedBy},#{item.sourceSite},#{item.dataSource})
        </foreach>
        ON DUPLICATE KEY UPDATE
        order_time = values(order_time),
        contract_no = values(contract_no),
        po_order_no = values(po_order_no),
        merchant = values(merchant),
        financing_order_no = values(financing_order_no),
        purchase_qty = values(purchase_qty),
        purchase_amount = values(purchase_amount),
        currency = values(currency),
        pay_time = values(pay_time),
        wms_order_no = values(wms_order_no),
        inbound_time = values(inbound_time),
        order_status = values(order_status),
        source_ent_id = values(source_ent_id),
        server_ent_id = values(server_ent_id),
        modified_at = VALUES(modified_at),
        modified_by = VALUES(modified_by),
        data_source = VALUES(data_source)
    </insert>

    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="purchaseMap" type="com.dstp.model.Purchase">
        <id property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="entOrderNo" column="ent_order_no"/>
        <result property="sourceSite" column="source_site"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="purchaseMap">
        SELECT id,ent_id,ent_order_no,source_site
        FROM doc_purchase_order
        WHERE (ent_id,ent_order_no,source_site) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.entId}, #{item.entOrderNo}, #{item.sourceSite})
        </foreach>
    </select>
</mapper>