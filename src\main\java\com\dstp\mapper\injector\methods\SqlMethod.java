package com.dstp.mapper.injector.methods;

import lombok.Getter;

@Getter
public enum SqlMethod {
    /**
     * 插入或更新
     */
    UPSERT("upsert", "插入或更新一条数据",
            """
            <script>
            INSERT INTO %s %s VALUES %s ON DUPLICATE KEY UPDATE id = LAST_INSERT_ID(id),%s
            </script>"""),

    /**
     * 批量插入或更新
     */
    BATCH_UPSERT("batchUpsert", "批量插入或更新多条数据",
            """
            <script>
            INSERT INTO %s %s VALUES %s ON DUPLICATE KEY UPDATE %s
            </script>
            """),

    /**
     * 批量插入
     */
    BATCH_INSERT("batchInsert", "批量插入多条数据",
        """
            <script>
            INSERT INTO %s %s VALUES %s
            </script>
            """);

    private final String method;
    private final String desc;
    private final String sql;

    SqlMethod(String method, String desc, String sql) {
        this.method = method;
        this.desc = desc;
        this.sql = sql;
    }
}
