package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * To C 销售关联采购
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_sale_order_to_c_purchase")
public class SaleToCPurchase extends BaseEntity {
    @NotNull(message = "2C销售单ID不能为空")
    private Long saleOrderToCId;
    private String goodsName;
    private String barCode;
    private String purchaseOrderNo;
    private Integer qty;
}
