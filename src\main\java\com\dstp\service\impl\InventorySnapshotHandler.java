package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.InventorySnapshotDetail;
import com.dstp.mapper.InventorySnapshotDetailMapper;
import com.dstp.mapper.InventorySnapshotMapper;
import com.dstp.model.InventorySnapshot;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class InventorySnapshotHandler extends MessageHandler {
    private final InventorySnapshotMapper inventorySnapshotMapper;
    private final InventorySnapshotDetailMapper inventorySnapshotDetailMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<InventorySnapshot> inventorySnapshotList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(inventorySnapshotList)) {
            log.warn("库存快照数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        inventorySnapshotList = distinct(inventorySnapshotList);

        // 1. 新增或更新库存快照
        inventorySnapshotMapper.batchUpsert(inventorySnapshotList);

        // 2. 查询已存在的库存快照ID
        List<Long> snapshotIds = batchGetParentPkId(inventorySnapshotList, inventorySnapshotMapper);

        // 3. 保存新库存快照明细
        List<InventorySnapshotDetail> inventorySnapshotDetails = batchSetManyChildFkId(inventorySnapshotList,
                InventorySnapshot::getInventoryList, InventorySnapshotDetail::setSnapshotId);
        inventorySnapshotDetailMapper.delete(new QueryWrapper<InventorySnapshotDetail>().in("snapshot_id", snapshotIds));
        inventorySnapshotDetailMapper.batchInsert(inventorySnapshotDetails);
    }
}
