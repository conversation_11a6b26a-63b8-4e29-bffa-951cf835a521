package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyDecimalDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 融资申请
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_financing_apply")
public class FinancingApply extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    private String financingOrderNo;
    private LocalDateTime applyTime;
    private Integer orderStatus;
    private LocalDateTime approvalTime;
    @JsonProperty("capital")
    private String capitalName;
    private String productType;
    @JsonDeserialize(using = MoneyDecimalDeserializer.class)
    private BigDecimal orderAmount;    // 单位：分（DECIMAL(22,0)）
    @JsonDeserialize(using = MoneyDecimalDeserializer.class)
    private BigDecimal applyAmount;    // 单位：分
    private String applyCurrency;
    @JsonDeserialize(using = MoneyDecimalDeserializer.class)
    private BigDecimal actualAmount;   // 单位：分
    private String actualCurrency;
    @TableField("intrate")
    private BigDecimal intRate;        // 利率（DECIMAL(10,8)）
    private LocalDateTime lendStartDate;
    private LocalDateTime lendEndDate;
    private String creditPlanNo;
    private Long sourceEntId;
    private Long serverEntId;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    private String linkId;

    public String getUniqueKeyString() {
        if (entId == null || financingOrderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + financingOrderNo + ":" + sourceSite;
    }
}