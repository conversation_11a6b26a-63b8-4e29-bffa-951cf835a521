package com.dstp.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品库通用商品信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("common_goods_info")
public class CommonGoods {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String goods_bar_code;
    private Long category_level1;
    private Long category_level2;
    private Long category_level3;
    private String category;
    private String brand;
    private String specification_type;
    private String goods_name;
    private String goods_english_name;
    private String origin_country;
    private String goods_creator_name;
    private String ent_creator_address;
    private String poison_element_flag;
    private String net_weight;
    private String package_type;
    private String foods_additive_flag;
    private String standard_unit;
    private String contract_unit;
    private Integer quality_guarantee_dates;
    private String rough_weight;
    private String goods_element;
    private BigDecimal average_recorded_price;
    private String extra_attribute1;
    private String extra_attribute2;
    private String extra_attribute3;
    private String extra_attribute4;
    private String extra_attribute5;
    private String create_user;
    private LocalDateTime create_time;
    private String modify_user;
    private LocalDateTime modify_time;
    private Integer branches;
    private Integer classify_status;
    private Integer baidu_train;
    private Long standard_brand_id;
    private String standard_brand_name;
}
