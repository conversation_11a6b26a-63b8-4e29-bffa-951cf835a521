package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 保税电商订单地址
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_bonded_ec_order_address")
public class BondedEcOrderAddress extends BaseEntity {
    @NotNull(message = "保税订单ID不能为空")
    private Long bondedOrderId;  // 对应主表ID
    private String country;
    private String province;
    private String city;
    private String district;
    private String telephone;
}