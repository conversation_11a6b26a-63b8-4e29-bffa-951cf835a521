package com.dstp.mapper.injector.methods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlScriptUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.keygen.Jdbc3KeyGenerator;
import org.apache.ibatis.executor.keygen.KeyGenerator;
import org.apache.ibatis.executor.keygen.NoKeyGenerator;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

import java.util.List;
import java.util.function.Predicate;

@Slf4j
public class BatchUpsert extends AbstractMethod {
    private Predicate<TableFieldInfo> insertPredicate;
    private Predicate<TableFieldInfo> updatePredicate;

    public BatchUpsert() {
        super(SqlMethod.BATCH_UPSERT.getMethod());
    }

    public BatchUpsert(Predicate<TableFieldInfo> insertPredicate, Predicate<TableFieldInfo> updatePredicate) {
        super(SqlMethod.BATCH_UPSERT.getMethod());
        this.insertPredicate = insertPredicate;
        this.updatePredicate = updatePredicate;
    }

    public BatchUpsert(String name, Predicate<TableFieldInfo> insertPredicate, Predicate<TableFieldInfo> updatePredicate) {
        super(name);
        this.insertPredicate = insertPredicate;
        this.updatePredicate = updatePredicate;
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        KeyGenerator keyGenerator = NoKeyGenerator.INSTANCE;
        SqlMethod sqlMethod = SqlMethod.BATCH_UPSERT;
        List<TableFieldInfo> fieldList = tableInfo.getFieldList();

        // 生成列脚本（无动态条件）
        String columnScript = SqlScriptUtils.convertTrim(
                        this.filterTableFieldInfo(fieldList, insertPredicate, TableFieldInfo::getInsertSqlColumn, EMPTY),
                        LEFT_BRACKET, RIGHT_BRACKET, null, COMMA);

        // 生成单行值的trim脚本（使用item前缀）,并用foreach包裹
        String valuesScript = SqlScriptUtils.convertForeach(
                SqlScriptUtils.convertTrim(
                        this.filterTableFieldInfo(fieldList, insertPredicate, i -> i.getInsertSqlProperty("item."), EMPTY),
                        LEFT_BRACKET, RIGHT_BRACKET, null, COMMA
        ), "list", null,"item", COMMA);

        // 生成更新部分脚本
        String setScript = SqlScriptUtils.convertTrim(
                this.filterTableFieldInfo(fieldList,
                        updatePredicate, i -> i.getColumn() + "=VALUES(" + i.getColumn() + ")", ",\n"),
                null, null, null, COMMA);

        String keyProperty = null;
        String keyColumn = null;
        // 主键逻辑处理,表包含主键处理逻辑,如果不包含主键当普通字段处理
        if (StringUtils.isNotBlank(tableInfo.getKeyProperty())) {
            if (tableInfo.getIdType() == IdType.AUTO) {
                keyGenerator = Jdbc3KeyGenerator.INSTANCE;
                keyProperty = tableInfo.getKeyProperty();
                keyColumn = tableInfo.getKeyColumn();
            } else if (tableInfo.getKeySequence() != null) {
                keyGenerator = TableInfoHelper.genKeyGenerator(this.methodName, tableInfo, builderAssistant);
                keyProperty = tableInfo.getKeyProperty();
                keyColumn = tableInfo.getKeyColumn();
            }
        }

        // 构建完整SQL
        String sql = String.format(sqlMethod.getSql(), tableInfo.getTableName(), columnScript, valuesScript, setScript);
        SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, modelClass);
        return this.addInsertMappedStatement(mapperClass, modelClass, getMethod(sqlMethod), sqlSource, keyGenerator, keyProperty, keyColumn);
    }

    private String getMethod(SqlMethod sqlMethod) {
        return StringUtils.isBlank(methodName) ? sqlMethod.getMethod() : methodName;
    }
}