package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 堆存信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_storage_voucher")
public class StorageVoucher extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId; // 企业 ID
    private String busiOrderNo; // DSTP合作协议号（契约订单号）
    @NotBlank(message = "堆存凭证编码不能为空")
    private String stackingVoucherCode; // 堆存凭证编码
    private String serviceOrderNo; // DSTP业务订单号
    private BigDecimal rentArea; // 仓库使用面积
    private String storageType; // 仓储类型：10标准件20标准托30包仓40租仓
    @NotBlank(message = "总条数不能为空")
    private Long totalCount; // 总条数
    @NotBlank(message = "分页大小不能为空")
    private Long pageSize; // 分页大小
    @NotBlank(message = "页码不能为空")
    private Long pageCount; // 页码
    private LocalDateTime atTime; // 快照时点
    private Long sourceEntId; // 数据来源企业 ID
    private Long serverEntId; // 服务商企业 ID
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite; // 来源站点
    private String linkId; // 链路 ID
    @TableField(exist = false, select = false)
    private List<StorageVoucherDetails> batchStockStatisticsVos; // 一对多关联：堆存明细

    public String getUniqueKeyString() {
        if (entId == null || stackingVoucherCode == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + stackingVoucherCode + ":" + sourceSite;
    }
}
