package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;

/**
 * 出库托盘
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_outbound_splint")
public class OutboundSplint extends BaseEntity {
    @NotNull(message = "出库单ID不能为空")
    private Long outboundOrderId;
    @JsonProperty("gNum")
    private String gNum;
    private String entGoodsNo;
    private String entGoodsName;
    private String splintNo;
    private BigDecimal splintWeight;
    @NotNull(message = "托盘长不能为空")
    private BigDecimal splintLong;
    @NotNull(message = "托盘宽不能为空")
    private BigDecimal splintWide;
    @NotNull(message = "托盘高不能为空")
    private BigDecimal splintHight;
    private String lotNo;
}