package com.dstp.mapper.injector;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.extension.injector.methods.InsertBatchSomeColumn;
import com.dstp.mapper.injector.methods.BatchInsert;
import com.dstp.mapper.injector.methods.BatchUpsert;
import com.dstp.mapper.injector.methods.Upsert;
import org.apache.ibatis.session.Configuration;

import java.util.List;

public class MySqlInjector extends DefaultSqlInjector {
    @Override
    public List<AbstractMethod> getMethodList(Configuration configuration, Class<?> mapperClass, TableInfo tableInfo) {
        List<AbstractMethod> methodList = super.getMethodList(configuration, mapperClass, tableInfo);
        methodList.add(new BatchInsert(i -> i.getFieldFill() != FieldFill.UPDATE));
        methodList.add(new BatchUpsert(null,i -> i.getFieldFill() != FieldFill.INSERT));
        methodList.add(new Upsert(null,i -> i.getFieldFill() != FieldFill.INSERT));
        return methodList;
    }
}