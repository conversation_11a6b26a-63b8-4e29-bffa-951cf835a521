package com.dstp.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 缓存服务基类
 * 提供通用的缓存处理逻辑
 */
@Slf4j
public abstract class BaseCacheService<T, K> {

    /**
     * 获取缓存管理器
     * @return 缓存管理器
     */
    protected abstract CacheManager getCacheManager();

    /**
     * 获取缓存名称
     * @return 缓存名称
     */
    protected abstract String getCacheName();

    /**
     * 获取缓存统计日志前缀
     * @return 缓存统计日志前缀
     */
    protected abstract String getCacheStatsPrefix();

    /**
     * 从数据库批量查询数据
     * @param keys 键列表
     * @return 数据列表
     */
    protected abstract List<T> batchQueryFromDb(List<K> keys);

    /**
     * 获取对象的键
     * @param obj 对象
     * @return 键
     */
    protected abstract K getKey(T obj);

    /**
     * 检查键是否有效
     * @param key 键
     * @return 是否有效
     */
    protected boolean isValidKey(K key) {
        if (key == null) {
            return false;
        }
        if (key instanceof String) {
            return StringUtils.isNotBlank((String) key);
        }
        return true;
    }

    /**
     * 批量获取数据，优先从缓存获取，缓存未命中的从数据库查询
     * @param keys 键列表
     * @return 数据映射
     */
    public Map<K, T> batchGet(List<K> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return new HashMap<>();
        }

        // 去重处理
        List<K> distinctKeys = keys.stream()
                .filter(this::isValidKey)
                .distinct()
                .toList();

        log.debug("批量查询{}，去重后键数量: {}", getCacheStatsPrefix(), distinctKeys.size());

        // 结果映射
        Map<K, T> result = new HashMap<>(distinctKeys.size());
        List<K> missedKeys = new ArrayList<>();

        // 先从缓存中获取
        CaffeineCache caffeineCache = (CaffeineCache) getCacheManager().getCache(getCacheName());
        if (caffeineCache != null) {
            Cache<Object, Object> nativeCache = caffeineCache.getNativeCache();
            
            for (K key : distinctKeys) {
                @SuppressWarnings("unchecked")
                T value = (T) nativeCache.getIfPresent(key);
                if (value != null) {
                    // 缓存命中
                    result.put(key, value);
                } else if (isValidKey(key)) {
                    // 缓存未命中，添加到需要查询的列表
                    missedKeys.add(key);
                }
            }
        } else {
            // 如果缓存不可用，则所有键都需要查询
            missedKeys.addAll(distinctKeys);
        }

        // 如果有缓存未命中的键，从数据库查询
        if (!missedKeys.isEmpty()) {
            log.debug("缓存未命中，从数据库查询{}，键数量: {}", getCacheStatsPrefix(), missedKeys.size());

            List<T> dataList = batchQueryFromDb(missedKeys);

            // 将查询结果放入结果映射
            Map<K, T> dbResult = dataList.stream()
                    .collect(Collectors.toMap(this::getKey, Function.identity()));

            result.putAll(dbResult);

            // 将查询结果放入缓存
            if (caffeineCache != null) {
                Cache<Object, Object> nativeCache = caffeineCache.getNativeCache();
                dbResult.forEach((key, value) -> {
                    if (isValidKey(key)) {
                        nativeCache.put(key, value);
                    }
                });
            }
        }

        return result;
    }

    /**
     * 定时打印缓存统计信息
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void printCacheStats() {
        CaffeineCache caffeineCache = (CaffeineCache) getCacheManager().getCache(getCacheName());
        if (caffeineCache != null) {
            Cache<Object, Object> nativeCache = caffeineCache.getNativeCache();
            CacheStats stats = nativeCache.stats();

            log.info("{}缓存统计 - 大小: {}, 命中率: {}, 命中数: {}, 未命中数: {}, 加载成功数: {}, 加载失败数: {}, 加载时间: {}ms",
                    getCacheStatsPrefix(),
                    nativeCache.estimatedSize(),
                    String.format("%.2f%%", stats.hitRate() * 100),
                    stats.hitCount(),
                    stats.missCount(),
                    stats.loadSuccessCount(),
                    stats.loadFailureCount(),
                    stats.totalLoadTime() / 1_000_000);
        }
    }
}
