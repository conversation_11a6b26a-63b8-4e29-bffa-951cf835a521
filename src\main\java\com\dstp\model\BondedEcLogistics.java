package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyLongDeserializer;
import com.dstp.deserializer.WeightDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 保税电商交运
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_bonded_ec_logistics")
public class BondedEcLogistics extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    private String busiOrderNo;
    private String serviceOrderNo;
    private String electricCode;
    private String cbepcomCode;
    private String electricName;
    private String warehouseId;
    private String warehouseName;
    @NotNull(message = "订单号不能为空")
    private String entOrderNo;
    private String wayBillNo;
    private LocalDateTime deliveryTime;
    private String logisticsName;
    private String logisticsCode;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long totalFee;
    private String busiType;
    private String totalWayBillNo;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal loadWeight;
    private BigDecimal volume;
    private String length;
    private String wide;
    private String high;
    private String specifications;
    private String sendType;
    private String sendProduct;
    private String subWayBillNo;
    private String tranNo;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long freight;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long insuredFee;
    private String currency;
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal weight;
    private Integer packNo;
    private String notes;
    private Integer isCheck;
    private String outboundOrderNo;
    private Long sourceEntId;
    private Long serverEntId;
    private String sourceSite;
    private String linkId;
    @TableField(exist = false, select = false)
    @JsonProperty("goodList")
    private List<BondedEcLogisticsGoods> goodsList;

    @Override
    public String getUniqueKeyString() {
        if (entId == null || entOrderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + entOrderNo + ":" + sourceSite;
    }
}

