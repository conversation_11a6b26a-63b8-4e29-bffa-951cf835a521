package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.CustomsClearanceGoods;
import com.dstp.mapper.CustomsClearanceGoodsMapper;
import com.dstp.mapper.CustomsClearanceMapper;
import com.dstp.model.CustomsClearance;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class CustomsClearanceHandler extends MessageHandler {
    private final CustomsClearanceMapper customsClearanceMapper;
    private final CustomsClearanceGoodsMapper customsClearanceGoodsMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<CustomsClearance> customsClearanceList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(customsClearanceList)) {
            log.warn("报关数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        customsClearanceList = distinct(customsClearanceList);

        // 1. 新增或更新报关单
        customsClearanceMapper.batchUpsert(customsClearanceList);

        // 2. 查询已存在的报关单ID
        List<Long> customsIds = batchGetParentPkId(customsClearanceList, customsClearanceMapper);

        // 3. 保存新报关单商品
        List<CustomsClearanceGoods> customsClearanceGoodsList = batchSetManyChildFkId(customsClearanceList,
                CustomsClearance::getGoodsList, CustomsClearanceGoods::setOrderId);
        // 删除旧报关单商品
        customsClearanceGoodsMapper.delete(new QueryWrapper<CustomsClearanceGoods>().in("order_id", customsIds));
        // 保存新报关单商品
        customsClearanceGoodsMapper.batchInsert(customsClearanceGoodsList);
    }
}
