package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.mapper.InventoryMapper;
import com.dstp.model.Goods;
import com.dstp.model.Inventory;
import com.dstp.model.Warehouse;
import com.dstp.service.GoodsService;
import com.dstp.service.MessageHandler;
import com.dstp.service.WarehouseService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class InventoryHandler extends MessageHandler {
    private final InventoryMapper inventoryMapper;
    private final WarehouseService warehouseService;
    private final GoodsService goodsService;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<Inventory> inventoryList = objectMapper.convertValue(dataNode, new TypeReference<>() {
        });

        if (CollectionUtils.isEmpty(inventoryList)) {
            log.warn("库存数据为空，跳过处理");
            return;
        }

        // 获取仓库信息
        Map<String, Warehouse> warehouseMap = warehouseService.getWarehouseByCodeList(
                inventoryList.stream().map(Inventory::getWarehouseId).toList());
        Map<String, Goods> goodsMap = goodsService.getGoodsByGoodsNoList(
                inventoryList.stream().map(inventory -> new Goods(inventory.getEntId(), inventory.getGoodsId())).toList());

        for (Inventory inventory : inventoryList) {
            int realStockNum = inventory.getRealStockNum();
            int lockStockNum = inventory.getLockStockNum();
            int realFrozenStockNum = inventory.getRealFrozenStockNum();
            // 计算总库存、可用库存
            int totalStockNum = realStockNum + lockStockNum;
            int usableStockNum = realStockNum - realFrozenStockNum;
            // 获取仓库名称
            String warehouseName = warehouseMap.getOrDefault(inventory.getWarehouseId(), new Warehouse()).getWarehouseName();
            // 获取商品信息
            Goods goods = goodsMap.get(inventory.getEntId() + ":" + inventory.getGoodsId());
            if (goods != null) {
                if (StringUtils.isBlank(inventory.getBarCode())) {
                    inventory.setBarCode(goods.getBarCode());
                }
                // 库存商品属性信息
                inventory.setParentBrand(goods.getParentBrand());
                inventory.setBrand(goods.getBrand());
                inventory.setFirstCategory(goods.getFirstCategory());
                inventory.setSecondCategory(goods.getSecondCategory());
                inventory.setThirdCategory(goods.getThirdCategory());

                long price = goods.getPrice() == null ? 0L : goods.getPrice();
                // 计算库存总额
                long amount = totalStockNum * price;
                inventory.setPrice(price);
                inventory.setAmount(amount);
            }

            inventory.setCurrency("CNY");
            inventory.setTotalStockNum(totalStockNum);
            inventory.setUsableStockNum(usableStockNum);
            inventory.setWarehouseName(warehouseName);
        }

        Long entId = inventoryList.get(0).getEntId();
        String inventoryVersion = inventoryList.get(0).getVersion();

        // 先删除同版本号的数据
        if (!StringUtils.isBlank(inventoryVersion)) {
            inventoryMapper.delete(new QueryWrapper<Inventory>()
                    .eq("version", inventoryVersion)
                    .eq("ent_id", entId)
                    .eq("created_by", "flink"));
        }

        // 批量插入
        inventoryMapper.batchInsert(inventoryList);
    }
}
