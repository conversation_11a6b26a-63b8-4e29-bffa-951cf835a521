package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.mapper.*;
import com.dstp.model.*;
import com.dstp.service.CommonGoodsService;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;

@Service
@Slf4j
@RequiredArgsConstructor
public class SaleToCHandler extends MessageHandler {
    private final SaleToCMapper saleToCMapper;
    private final SaleToCAddressMapper saleToCAddressMapper;
    private final SaleToCGoodsMapper saleToCGoodsMapper;
    private final SaleToCPurchaseMapper saleToCPurchaseMapper;
    private final CommonGoodsService commonGoodsService;
    private final ObjectMapper objectMapper;

    public void process(JsonNode dataNode) {
        List<SaleToC> saleToCList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(saleToCList)) {
            log.warn("销售2C数据为空，跳过处理");
            return;
        }

        // 0. 去重
        saleToCList = distinct(saleToCList);

        // 1. 新增或更新ToC销售订单
        saleToCMapper.batchUpsert(saleToCList);

        // 2. 查询已存在的2C销售ID
        List<Long> saleIds = batchGetParentPkId(saleToCList, saleToCMapper);

        // 3. 处理销售订单地址信息
        List<SaleToCAddress> saleToCAddressList = batchSetOneChildFkId(saleToCList,
                SaleToC::getRecipient, SaleToCAddress::setSaleOrderToCId);
        updateData(saleToCAddressList, saleIds, saleToCAddressMapper::delete, saleToCAddressMapper::batchInsert);

        // 4. 处理销售订单商品信息
        Map<String, CommonGoodsAttr> commonGoodsAttrMap = commonGoodsService.getGoodsAttrByBarcodeList(
                saleToCList.stream()
                        .flatMap(saleToB -> Optional.ofNullable(saleToB.getGoodsList())
                                .orElse(List.of())
                                .stream()
                                .map(SaleToCGoods::getBarCode))
                        .toList());

        List<SaleToCGoods> saleToCGoodsList = saleToCList.stream()
                .flatMap(saleToC -> Optional.ofNullable(saleToC.getGoodsList())
                        .orElse(List.of())
                        .stream()
                        .peek(saleToCGoods -> {
                            saleToCGoods.setSaleOrderToCId(saleToC.getId());
                            // 获取商品品类
                            CommonGoodsAttr attr = commonGoodsAttrMap.get(saleToCGoods.getBarCode());
                            if (attr != null) {
                                saleToCGoods.setCategory1(attr.getFirstCategory());
                                saleToCGoods.setCategory2(attr.getSecondCategory());
                            }
                        }))
                .toList();
        updateData(saleToCGoodsList, saleIds, saleToCGoodsMapper::delete, saleToCGoodsMapper::batchInsert);

        // 5. 处理销售订单采购信息
        List<SaleToCPurchase> saleToCPurchaseList = batchSetManyChildFkId(saleToCList,
                SaleToC::getPurchaseList, SaleToCPurchase::setSaleOrderToCId);
        updateData(saleToCPurchaseList, saleIds, saleToCPurchaseMapper::delete, saleToCPurchaseMapper::batchInsert);
    }

    private <T> void updateData(List<T> dataList, List<Long> saleIds, Consumer<QueryWrapper<T>> deleteMethod, Consumer<List<T>> insertMethod) {
        if (!CollectionUtils.isEmpty(dataList)) {
            // 删除旧数据
            deleteMethod.accept(new QueryWrapper<T>().in("sale_order_to_c_id", saleIds));
            // 保存新数据
            insertMethod.accept(dataList);
        }
    }
}