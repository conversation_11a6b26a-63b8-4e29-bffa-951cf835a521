package com.dstp.service;

import com.dstp.model.ExceptionLog;
import com.dstp.mapper.ExceptionLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

/**
 * 异常服务
 * 提供异常日志记录、告警和查询功能
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ExceptionService {
    @Value("${wechat.webhook.url}")
    private String webhookUrl;

    @Value("${wechat.alarm.enabled}")
    private boolean enableAlarm;

    @Value("${spring.profiles.active}")
    private String projectEnv;

    @Value("${spring.application.name}")
    private String projectName;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ExceptionLogMapper exceptionLogMapper;

    /**
     * 记录错误并发送告警
     *
     * @param exceptionDescription 异常消息
     * @param exception 异常对象
     * @param topic 主题
     * @param offset 偏移量
     */
    public void alarm(String exceptionDescription, Exception exception, String topic, Long offset) {
        String stackTrace = getStackTrace(exception);
        String safeTopic = topic != null ? topic : "N/A";
        Long safeOffset = offset != null ? offset : -1L;

        // 异步发送告警，避免阻塞主流程
        if (enableAlarm) {
            sendAlarmAsync(exceptionDescription, stackTrace, safeTopic, safeOffset);
        }

        // 记录异常日志
        logException("ERROR", stackTrace, exceptionDescription, safeTopic, safeOffset);
    }

    /**
     * 记录警告信息
     *
     * @param errorDescription 错误描述
     * @param topic 主题
     * @param offset 偏移量
     */
    public void error(Exception exception, String errorDescription, String topic, Long offset) {
        log.error("{} topic: {}, offset: {}, error: {}", errorDescription, topic, offset, exception.getMessage());
        logException("ERROR", getStackTrace(exception), errorDescription, topic, offset);
    }

    /**
     * 记录警告信息
     *
     * @param warnDescription 警告描述
     * @param topic 主题
     * @param offset 偏移量
     */
    public void warn(String warnDescription, String topic, Long offset) {
        log.warn("{} topic: {}, offset: {}", warnDescription, topic, offset);
        logException("WARN", null, warnDescription, topic, offset);
    }


    /**
     * 记录异常日志（增强版）
     */
    private void logException(String logType, String stackTrace, String description, String topic, Long offset) {
        try {
            ExceptionLog exceptionLog = new ExceptionLog();
            exceptionLog.setMessage(description);
            exceptionLog.setContent(stackTrace);
            exceptionLog.setTopic(topic);
            exceptionLog.setOffset(offset);
            exceptionLog.setType(logType);

            exceptionLogMapper.insert(exceptionLog);
        } catch (Exception e) {
            // 如果记录异常日志时发生异常，记录到应用日志中
            log.error("记录异常日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 异步发送告警
     */
    @Async("exceptionExecutor")
    public void sendAlarmAsync(String exceptionMessage, String stackTrace, String topic, Long offset) {
        try {
            wechat(exceptionMessage, stackTrace, topic, offset);
            CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("异步发送告警失败: {}", e.getMessage(), e);
            CompletableFuture.failedFuture(e);
        }
    }

    /**
     * 发送微信告警
     */
    public void wechat(String exceptionMessage, String stackTrace, String topic, Long offset) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 优化告警消息格式
            String jsonPayload = String.format(
                    """
                    {"msgtype": "markdown", "markdown": {"content": "### ⚠️ %s(%s)程序执行异常 ⚠️
                    > **队列信息**：%s(%s)
                    > **异常时间**：%s
                    > **异常信息**：%s
                    > **堆栈跟踪**：
                    ```
                    %s
                    ```"}}
                    """,
                    projectName, projectEnv, topic, offset,
                    LocalDateTime.now(), exceptionMessage, stackTrace
            );

            HttpEntity<String> request = new HttpEntity<>(jsonPayload, headers);

            restTemplate.postForEntity(webhookUrl, request, String.class);
        } catch (Exception e) {
            // 处理发送失败的情况，例如记录日志
            log.error("发送微信通知失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 获取异常堆栈信息
     */
    public String getStackTrace(Exception e) {
        if (e == null) return "";

        StringWriter sw = new StringWriter();
        e.printStackTrace(new PrintWriter(sw));
        String stackTrace = sw.toString();
        return stackTrace.length() > 1000 ? stackTrace.substring(0, 1000) + "..." : stackTrace;
    }
}
