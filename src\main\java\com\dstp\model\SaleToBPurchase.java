package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * To B 销售关联采购
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_sale_order_to_b_purchase")
public class SaleToBPurchase extends BaseEntity {
    @NotNull(message = "2B销售单ID不能为空")
    private Long saleOrderId;
    private String goodsName;
    private String barCode;
    private String purchaseOrderNo;
    private Integer qty;
}
