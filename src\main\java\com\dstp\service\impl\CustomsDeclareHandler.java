package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.mapper.CustomsDeclareGoodsMapper;
import com.dstp.mapper.CustomsDeclareMapper;
import com.dstp.model.CustomsDeclare;
import com.dstp.model.CustomsDeclareGoods;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class CustomsDeclareHandler extends MessageHandler {
    private final CustomsDeclareMapper customsDeclareMapper;
    private final CustomsDeclareGoodsMapper customsDeclareGoodsMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<CustomsDeclare> customsDeclareList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(customsDeclareList)) {
            log.warn("报关数据为空，跳过处理！");
            return;
        }

        // 1. 对customsDeclareList进行去重，根据customerCode和declareNo两个字段判断是否完全相同
        customsDeclareList = distinct(customsDeclareList);

        // 2. 查询数据表中相同customerCode和declareNo的报关单信息
        List<CustomsDeclare> existCustomsDeclareList = customsDeclareMapper.selectByUniqueKeys(customsDeclareList);
        Map<String, CustomsDeclare> existCustomsDeclareMap = existCustomsDeclareList.stream()
                .collect(Collectors.toMap(CustomsDeclare::getUniqueKeyString, customsDeclare -> customsDeclare));

        // 3. 判断报关单是否有历史数据，如果有则判断是否是交换中心接口回传的数据，如果是则加入更新列表，否则跳过，若无则加入新增列表
        List<CustomsDeclare> insertCustomsDeclareList = new ArrayList<>();
        List<CustomsDeclare> updateCustomsDeclareList = new ArrayList<>();
        for (CustomsDeclare customsDeclare : customsDeclareList) {
            String synchronousMode = "INTERFACE_BACKHAUL";
            String ieFlag = customsDeclare.getIeFlag();
            customsDeclare.setSynchronousMode(synchronousMode);
            customsDeclare.setCommitTime(LocalDateTime.now());
            customsDeclare.setUpdateTime(LocalDateTime.now());
            if (ieFlag != null && ieFlag.equals("I")) {
                customsDeclare.setIeFlag("IMPORT");
            } else if (ieFlag != null && ieFlag.equals("E")) {
                customsDeclare.setIeFlag("EXPORT");
            }

            String uniqueKey = customsDeclare.getUniqueKeyString();
            if (existCustomsDeclareMap.containsKey(uniqueKey)) {
                CustomsDeclare existCustomsDeclare = existCustomsDeclareMap.get(uniqueKey);
                if (existCustomsDeclare.getSynchronousMode().equals(synchronousMode)) {
                    customsDeclare.setId(existCustomsDeclare.getId());
                    updateCustomsDeclareList.add(customsDeclare);
                } else {
                    log.warn("已存在非交换中心接口回传的报关单数据，跳过处理！");
                }
            } else {
                insertCustomsDeclareList.add(customsDeclare);
            }
        }

        // 4. 新增报关信息
        List<Long> customsDeclareIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(insertCustomsDeclareList)) {
            customsDeclareMapper.batchInsert(insertCustomsDeclareList);
            customsDeclareIds.addAll(batchGetParentPkId(insertCustomsDeclareList, customsDeclareMapper));
        }

        // 5. 更新报关信息
        if (!CollectionUtils.isEmpty(updateCustomsDeclareList)) {
            customsDeclareMapper.updateById(updateCustomsDeclareList);
            customsDeclareIds.addAll(updateCustomsDeclareList.stream().map(CustomsDeclare::getId).toList());
        }
        insertCustomsDeclareList.addAll(updateCustomsDeclareList);

        // 6. 更新申报信息中多个码表属性信息
        customsDeclareMapper.updateAttrByIds(customsDeclareIds);

        // 7. 保存新报关清单商品
        List<CustomsDeclareGoods> customsDeclareGoodsList = insertCustomsDeclareList.stream()
                .flatMap(customsDeclare -> {
                    // 为每个报关单的商品生成独立序号
                    AtomicInteger gNum = new AtomicInteger(1); // 每
                    return Optional.ofNullable(customsDeclare.getGoodsList())
                            .orElse(List.of())
                            .stream()
                            .peek(customsDeclareGoods -> {
                                customsDeclareGoods.setCustomsClearanceId(customsDeclare.getId());
                                customsDeclareGoods.setGNum(String.valueOf(gNum.getAndIncrement())); // 添加序号字段
                            });
                })
                .toList();
        customsDeclareGoodsMapper.delete(new QueryWrapper<CustomsDeclareGoods>().in("customs_clearance_id", customsDeclareIds));
        customsDeclareGoodsMapper.batchInsert(customsDeclareGoodsList);

        // 8. 申报信息-商品信息中的码表属性信息
        customsDeclareGoodsMapper.updateAttrByCustomsClearanceIds(customsDeclareIds);
    }
}
