package com.dstp.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * Kafka Message元数据信息
 */
@Data
public class Metadata {
    private Long entId;
    private String entName;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String entLicenseNo;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long sourceEntId;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String sourceEntName;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String sourceEntLicenseNo;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long serverEntId;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String serverEntName;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String serverEntLicenseNo;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String sourceSite;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String inventoryVersion;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private LocalDateTime inventoryDate;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long kafkaOffset;
}
