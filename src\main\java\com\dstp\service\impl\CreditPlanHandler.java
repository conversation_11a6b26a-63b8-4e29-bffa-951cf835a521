package com.dstp.service.impl;

import com.dstp.mapper.CreditPlanMapper;
import com.dstp.model.CreditPlan;
import com.dstp.service.ExchangeRateService;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class CreditPlanHandler extends MessageHandler {
    private final CreditPlanMapper creditPlanMapper;
    private final ExchangeRateService exchangeRateService;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<CreditPlan> creditPlanList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(creditPlanList)) {
            log.warn("融资授信数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        creditPlanList = distinct(creditPlanList);

        // 1. 新增或更新授信计划
        creditPlanList.forEach(creditPlan -> {
            // 查询汇率，计算授信额度（人民币）
            BigDecimal exchangeRate;
            String rateMonth = "2020-01";
            String currency = creditPlan.getCurrency();
            LocalDateTime timeStart = creditPlan.getTimeStart();

            // 如果授信期限大于等于2020年，则使用当前月汇率
            DateTimeFormatter formater = DateTimeFormatter.ofPattern("yyyy-MM");
            if (timeStart.getYear() >= 2020) {
                rateMonth = timeStart.format(formater);
            }

            // 查询汇率
            exchangeRate = exchangeRateService.getExchangeRateByCurrencyAndYM(currency, rateMonth);

            // 设置授信额度（转人民币并换算为单位分）
            BigDecimal totalLine = creditPlan.getTotalLine();
            if (totalLine != null) {
                creditPlan.setTotalLineCny(totalLine.multiply(exchangeRate));
            }
        });
        creditPlanMapper.batchUpsert(creditPlanList);
    }
}
