package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyLongDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 保税电商交运商品
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_bonded_ec_logistics_good")
public class BondedEcLogisticsGoods extends BaseEntity {
    @NotNull(message = "保税交运ID不能为空")
    private Long logisticsId;
    private String entGoodsNo;
    private Integer amount;
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long unitPrice;
    private String batchId;
    private LocalDateTime productionTime;
    private LocalDateTime expDate;
}