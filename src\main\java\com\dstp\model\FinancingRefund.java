package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 融资还款
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_refund_info")
public class FinancingRefund extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    @NotBlank(message = "还款单号不能为空")
    private String refundOrderNo;
    @NotNull(message = "还款日期不能为空")
    private LocalDateTime refundDate;
    @NotBlank(message = "还款币种不能为空")
    private String refundCurrency;
    private Long sourceEntId;
    private Long serverEntId;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    private String linkId;
    @TableField(exist = false, select = false)
    private List<FinancingRefundDetail> refundList;

    public String getUniqueKeyString() {
        if (entId == null || refundOrderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + refundOrderNo + ":" + sourceSite;
    }
}