package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 省电报关单
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("tb_transaction_customs_clearance")
public class CustomsDeclare extends BaseEntity {
    @JsonProperty("entryNo")
    @NotBlank(message = "申报单号不能为空")
    private String declareNo;
    @NotBlank(message = "申报地海关代码不能为空")
    private String declareCode;                  // 申报地海关代码
    private String customMasterName;             // 申报地海关名称
    private String entryExitCode;                // 进出境关别代码
    private String iePortName;                   // 进出境关别名称
    @NotBlank(message = "监管方式代码不能为空")
    private String superviseType;                // 监管方式代码
    private String supvModeCddeName;             // 监管方式名称
    private String cutMode;
    private String cutModeName;
    private String licenseNo;
    private LocalDateTime ieDate;
    private String cusDecStatus;
    private String cusDecStatusName;
    private String cusCiqNo;
    @NotBlank(message = "进出口标志不能为空")
    private String ieFlag;                       // 进出口标志(出口：EXPORT，进口：IMPORT)
    private String agentScc;
    private String customsClearanceCode;
    private String customsClearanceName;
    private LocalDateTime declareTime;
    private LocalDateTime releaseTime;
    private LocalDateTime customsClearanceDate;
    private String foreignConsigneeNo;
    private String foreignConsignee;
    private String domesticConsigneeCustomsNo;
    private String domesticConsigneeNo;
    private String domesticConsignee;
    private String ownerScc;
    private String consumUnitName;
    private String consumUnitNo;
    private String transportBlno;
    @JsonProperty("blno")
    private String billNo;
    private String packingListNo;
    private String containerNo;
    private String contractNo;
    private String transportMode;                // 运输方式代码
    private String cusTrafModeName;              // 运输方式名称
    private String transportTool;
    private String voyageNo;
    private String checkNo;
    private String passportNo;
    private String beginCountry;                 // 启运国/运抵国代码
    private String cusTradeCountryName;          // 启运国/运抵国名称
    private String beginPort;                    // 启运港代码
    private String despPortCodeName;             // 启运港名称
    private String tradeCountry;                 // 贸易国海关代码
    private String cusTradeNationCodeName;       // 贸易国海关名称
    private String designationPort;              // 经停港/指运港代码
    private String distinatePortName;            // 经停港/指运港名称
    private String feeMark;
    private String feeCurr;
    private String contaCount;
    private String entryTypeName;
    private String markNo;
    @JsonIgnore
    private String portsEntry;
    // 企业库之前版本的数据映射错误，portsEntry和ciqEntyPortCodeName赋值弄饭，当前照旧
    // 阻止JSON中的portsEntry映射到portsEntry属性
    @JsonProperty("portsEntry")
    private String ciqEntyPortCodeName;
    private BigDecimal grossWeight;
    private BigDecimal netWeight;
    private String wrapType;                     // 包装种类代码
    private String wrapTypeName;                 // 包装种类名称
    private String goodsPlace;
    private LocalDateTime orderCreateTime;
    private LocalDateTime inspectionTime;
    private String inboundOrderNo;
    private String outboundOrderNo;
    private String transMode;                    // 成交方式代码
    private String transModeName;                // 成交方式名称
    private String insurMark;
    private String insurCurr;
    private String otherMark;
    private String otherCurr;
    private Integer packNo;
    private String dataLocation;                 // 1
    private Long declareTotalPrice;
    private String currency;
    private String declareCategory;
    private Boolean isSwitch;
    private Boolean isLabor;
    @JsonProperty("entLicenseNo")
    @NotBlank(message = "数据所属编码不能为空")
    private String customerCode;
    @JsonProperty("entName")
    @NotBlank(message = "数据所属主体不能为空")
    private String customerName;
    private String overseasCustomerName;
    private String sourceMaterial;
    private String noteNo;
    private String docCode;
    @NotBlank(message = "同步方式不能为空")
    private String synchronousMode;              // INTERFACE_BACKHAUL
    @NotNull(message = "提交时间不能为空")
    private LocalDateTime commitTime;
    private LocalDateTime updateTime;
    private String entOrderNo;
    private String busiOrderNo;
    private String serviceOrderNo;
    private String portBonded;
    @TableField("total_trade_CNY")
    private BigDecimal totalTradeCNY;
    @TableField("total_trade_USD")
    private BigDecimal totalTradeUSD;
    @TableField("total_customs_trade_CNY")
    private BigDecimal totalCustomsTradeCNY;
    @TableField("total_customs_trade_USD")
    private BigDecimal totalCustomsTradeUSD;
    private String notes;
    private BigDecimal feeRate;
    private BigDecimal insurRate;
    private BigDecimal otherRate;
    private String feeMarkName;
    private String otherMarkName;
    private String otherCurrName;
    @JsonProperty("sourceEntLicenseNo")
    @NotBlank(message = "数据来源编码不能为空")
    private String dataSourceCode;
    @JsonProperty("sourceEntName")
    @NotBlank(message = "数据来源企业不能为空")
    private String dataSourceName;
    @TableField(exist = false, select = false)
    private List<CustomsDeclareGoods> goodsList;

    @Override
    public String getUniqueKeyString() {
        if (customerCode == null || declareNo == null) {
            return null;
        }
        return customerCode + ":" + declareNo;
    }
}
