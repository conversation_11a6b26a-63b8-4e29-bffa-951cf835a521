package com.dstp.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.mapper.WarehouseMapper;
import com.dstp.model.Warehouse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 仓库服务
 * 提供仓库信息查询和缓存管理
 */
@Service
@Slf4j
@RequiredArgsConstructor
@CacheConfig(cacheNames = "warehouseCache", cacheManager = "warehouseCacheManager")
public class WarehouseService extends BaseCacheService<Warehouse, String> {

    private final WarehouseMapper warehouseMapper;
    private final CacheManager warehouseCacheManager;

    /**
     * 根据仓库编号获取仓库信息
     * 使用缓存提高查询性能
     *
     * @param warehouseCode 仓库编号
     * @return 仓库信息
     */
    @Cacheable(key = "#warehouseCode", condition = "#warehouseCode != null", unless = "#result == null")
    public Warehouse getWarehouseByCode(String warehouseCode) {
        if (StringUtils.isBlank(warehouseCode)) {
            return null;
        }

        log.debug("缓存未命中，从数据库查询企业信息: 仓库编号={}", warehouseCode);
        return warehouseMapper.selectOne(new QueryWrapper<Warehouse>()
                .select("warehouse_id", "warehouse_name")
                .eq("warehouse_id", warehouseCode));
    }

    /**
     * 根据仓库编码列表批量获取仓库信息
     * 使用缓存提高查询性能
     * 优化批量查询逻辑，避免逐个查询
     *
     * @param warehouseCodes 仓库编号列表
     * @return 仓库映射信息
     */
    public Map<String, Warehouse> getWarehouseByCodeList(List<String> warehouseCodes) {
        if (CollectionUtils.isEmpty(warehouseCodes)) {
            return new HashMap<>();
        }

        // 去重处理并标准化条形码
        List<String> distinctWarehouseCodes = warehouseCodes.stream()
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();

        return batchGet(distinctWarehouseCodes);
    }

    @Override
    protected CacheManager getCacheManager() {
        return warehouseCacheManager;
    }

    @Override
    protected String getCacheName() {
        return "warehouseCache";
    }

    @Override
    protected String getCacheStatsPrefix() {
        return "仓库";
    }

    @Override
    protected List<Warehouse> batchQueryFromDb(List<String> keys) {
        return warehouseMapper.selectList(new QueryWrapper<Warehouse>()
                .select("warehouse_id", "warehouse_name")
                .in("warehouse_id", keys));
    }

    @Override
    protected String getKey(Warehouse obj) {
        return obj.getWarehouseId();
    }
}
