package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.mapper.ImportExportLogisticsContainerMapper;
import com.dstp.mapper.ImportExportLogisticsExpansionMapper;
import com.dstp.mapper.ImportExportLogisticsMapper;
import com.dstp.mapper.ImportExportLogisticsTimeMapper;
import com.dstp.model.*;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class ImportExportLogisticsHandler extends MessageHandler {
    private final ImportExportLogisticsMapper importExportLogisticsMapper;
    private final ImportExportLogisticsContainerMapper importExportLogisticsContainerMapper;
    private final ImportExportLogisticsExpansionMapper importExportLogisticsExpansionMapper;
    private final ImportExportLogisticsTimeMapper importExportLogisticsTimeMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<ImportExportLogistics> importExportLogisticsList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(importExportLogisticsList)) {
            log.warn("进出口物流数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        importExportLogisticsList = distinct(importExportLogisticsList);

        // 1. 新增或更新进出口物流
        importExportLogisticsMapper.batchUpsert(importExportLogisticsList);

        // 2. 查询已存在的进出口物流ID
        List<Long> documentIds = batchGetParentPkId(importExportLogisticsList, importExportLogisticsMapper);

        // 3. 保存新进出口物流单据-集装箱信息
        List<ImportExportLogisticsContainer> importExportLogisticsContainersList = batchSetManyChildFkId(importExportLogisticsList,
                ImportExportLogistics::getContainerList, ImportExportLogisticsContainer::setDocumentId);
        // 删除旧进出口物流单据-集装箱信息
        importExportLogisticsContainerMapper.delete(new QueryWrapper<ImportExportLogisticsContainer>().in("document_id", documentIds));
        // 写入新进出口物流单据-集装箱信息
        importExportLogisticsContainerMapper.batchInsert(importExportLogisticsContainersList);

        // 4. 保存新进出口物流单据-拓展信息
        List<ImportExportLogisticsExpansion> importExportLogisticsExpansionList = batchSetManyChildFkId(importExportLogisticsList,
                ImportExportLogistics::getExpansionList, ImportExportLogisticsExpansion::setDocumentId);
        // 删除旧进出口物流单据-拓展信息
        importExportLogisticsExpansionMapper.delete(new QueryWrapper<ImportExportLogisticsExpansion>().in("document_id", documentIds));
        // 写入新进出口物流单据-拓展信息
        importExportLogisticsExpansionMapper.batchInsert(importExportLogisticsExpansionList);

        // 5. 保存新进出口物流单据-时间信息
        List<ImportExportLogisticsTime> importExportLogisticsTimeList = batchSetManyChildFkId(importExportLogisticsList,
                ImportExportLogistics::getTimeList, ImportExportLogisticsTime::setDocumentId);
        // 删除旧进出口物流单据-时间信息
        importExportLogisticsTimeMapper.delete(new QueryWrapper<ImportExportLogisticsTime>().in("document_id", documentIds));
        // 写入新进出口物流单据-时间信息
        importExportLogisticsTimeMapper.batchInsert(importExportLogisticsTimeList);
    }
}
