package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 入库车辆
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_inbound_vehicle")
public class InboundVehicle extends BaseEntity {
    @NotNull(message = "入库单ID不能为空")
    private Long inboundOrderId;
    @JsonProperty("gNum")
    private String gNum;
    private String vehicleNo;
    private String vehicleType;
    private Integer carpool;
    private String notes;
}
