package com.dstp.consumer;

import com.dstp.service.MessageHandler;
import com.dstp.service.MessageHandlerFactory;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
@RequiredArgsConstructor
public class BatchMessageProcessor {
    // 消息存储，使用ConcurrentHashMap保证线程安全
    private final Map<String, List<ConsumerRecord<String, String>>> messageMap = new ConcurrentHashMap<>();

    // 线程池和调度器
    private ScheduledExecutorService scheduler;
    private ExecutorService processorExecutor;

    // 依赖注入
    private final MessageHandlerFactory messageHandlerFactory;
    private final KafkaMessageTransformer kafkaMessageTransformer;

    // 可配置参数，从配置文件中读取
    @Value("${batch.processor.max-batch-size:500}")
    private int maxBatchSize;

    @Value("${batch.processor.initial-delay-seconds:5}")
    private int initialDelaySeconds;

    @Value("${batch.processor.fixed-delay-seconds:10}")
    private int fixedDelaySeconds;

    @Value("${batch.processor.thread-pool-size:4}")
    private int threadPoolSize;

    // 性能监控指标
    private final Map<String, AtomicInteger> topicMessageCounts = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        // 创建处理线程池，用于并行处理不同topic的消息
        processorExecutor = Executors.newFixedThreadPool(threadPoolSize,
                new ThreadFactory() {
                    private final AtomicInteger counter = new AtomicInteger(1);

                    @Override
                    public Thread newThread(Runnable r) {
                        Thread thread = new Thread(r, "batch-processor-" + counter.getAndIncrement());
                        thread.setDaemon(true);
                        return thread;
                    }
                });

        // 创建调度器，定期执行批处理
        scheduler = Executors.newScheduledThreadPool(1);
        scheduler.scheduleWithFixedDelay(() -> {
            try {
                this.batchProcess();
            } catch (Exception e) {
                log.error("批量处理失败!", e);
            }
        }, initialDelaySeconds, fixedDelaySeconds, TimeUnit.SECONDS);

        log.info("批处理器初始化完成，最大批量大小: {}, 处理间隔: {}秒, 线程池大小: {}",
                maxBatchSize, fixedDelaySeconds, threadPoolSize);
    }

    @PreDestroy
    public void destroy() {
        if (processorExecutor != null) {
            processorExecutor.shutdown();
            try {
                if (!processorExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    processorExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                processorExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        if (scheduler != null) {
            scheduler.shutdownNow();
        }

        log.info("批处理器已关闭");
    }

    /**
     * 添加消息到批处理队列
     * 如果队列大小达到阈值，触发立即处理
     */
    public void addToBatch(String topic, ConsumerRecord<String, String> message) {
        List<ConsumerRecord<String, String>> messages = messageMap.computeIfAbsent(topic, k -> new ArrayList<>());

        synchronized (messages) {
            messages.add(message);

            // 记录消息数量，用于监控
            topicMessageCounts.computeIfAbsent(topic, k -> new AtomicInteger(0))
                    .incrementAndGet();

            // 如果达到最大批量大小，立即触发处理
            if (messages.size() >= maxBatchSize) {
                processSingleTopic(topic, new ArrayList<>(messages));
                messages.clear();
                log.info("Topic: {} 达到最大批量大小 {}, 触发立即处理", topic, maxBatchSize);
            }
        }
    }

    /**
     * 批量处理所有主题的消息
     * 使用线程池并行处理不同主题
     */
    private void batchProcess() {
        // 记录处理开始时间
        long startTime = System.currentTimeMillis();
        log.debug("开始批量处理消息...");

        // 收集需要处理的主题和消息
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (Map.Entry<String, List<ConsumerRecord<String, String>>> entry : messageMap.entrySet()) {
            String topic = entry.getKey();
            List<ConsumerRecord<String, String>> messageList = entry.getValue();

            // 使用同步块安全地获取消息列表副本并清空原列表
            List<ConsumerRecord<String, String>> messagesToProcess;
            synchronized (messageList) {
                if (CollectionUtils.isEmpty(messageList)) {
                    continue;
                }
                messagesToProcess = new ArrayList<>(messageList);
                messageList.clear();
            }

            // 提交到线程池异步处理
            if (!messagesToProcess.isEmpty()) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(
                        () -> processSingleTopic(topic, messagesToProcess),
                        processorExecutor
                );
                futures.add(future);
            }
        }

        // 等待所有处理完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 记录处理耗时
        long processingTime = System.currentTimeMillis() - startTime;
        log.debug("批量处理完成，耗时: {}ms", processingTime);

        // 如果处理时间过长，考虑调整批处理参数
        if (processingTime > fixedDelaySeconds * 1000L) {
            log.warn("批处理耗时({})超过了调度间隔({}秒)，请考虑调整批处理参数",
                    processingTime, fixedDelaySeconds);
        }
    }

    /**
     * 处理单个主题的消息批次
     */
    private void processSingleTopic(String topic, List<ConsumerRecord<String, String>> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }

        long startTime = System.currentTimeMillis();
        int messageCount = messageList.size();

        try {
            log.info("处理Topic: {}, 消息数量: {}", topic, messageCount);

            MessageHandler handler = messageHandlerFactory.getHandler(topic);
            if (handler != null) {
                JsonNode jsonNode = kafkaMessageTransformer.batchTransformer(messageList);
                handler.process(jsonNode);

                // 记录处理时间
                long processingTime = System.currentTimeMillis() - startTime;

                log.info("Topic: {} 处理完成, 消息数: {}, 耗时: {}ms, 平均: {}ms/条",
                        topic, messageCount, processingTime,
                        messageCount > 0 ? processingTime / messageCount : 0);
            } else {
                log.warn("未知的Topic: {}, 消息将被丢弃", topic);
            }
        } catch (Exception e) {
            log.error("处理Topic: {} 失败, 消息数: {}, 错误: {}", topic, messageCount, e.getMessage(), e);
        }
    }
}