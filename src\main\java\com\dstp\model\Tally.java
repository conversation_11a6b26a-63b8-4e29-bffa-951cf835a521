package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 理货信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_tally_header")
public class Tally extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId; // 企业 ID
    @NotBlank(message = "理货单号不能为空")
    private String orderNo; // 理货单号
    private String serviceOrderNo; // 服务需求单号
    private String busiOrderNo; // 业务订单号（契约订单号）
    private String inboundOrderNo; // 入仓订单号
    private Long inboundOrderId; // 入仓订单 ID
    private String warehouseId; // 仓库 ID
    @JsonProperty("warehouseName")
    private String warehouse; // 仓库
    private String bookNo; // 账册编码
    @JsonProperty("superviseType")
    private String supervisionType; // 申报监管方式
    @JsonProperty("billNo")
    private String waybill; // 提单号
    private String contractNo; // 合同协议号
    private String transportMode; // 运输方式
    private LocalDateTime arrivalTime; // 到仓时间
    private LocalDateTime tallyTime; // 理货时间
    private LocalDateTime tallyCompletedTime; // 理货完成时间
    private LocalDateTime shelfTime; // 上架时间
    private Long sourceEntId; // 数据来源企业 ID
    private Long serverEntId; // 服务商企业 ID
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite; // 来源站点
    private String linkId; // 链路 ID
    @TableField(exist = false, select = false)
    private List<TallyGoods> goodsList; // 一对多关联：理货单商品

    public String getUniqueKeyString() {
        if (entId == null || orderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + orderNo + ":" + sourceSite;
    }
}
