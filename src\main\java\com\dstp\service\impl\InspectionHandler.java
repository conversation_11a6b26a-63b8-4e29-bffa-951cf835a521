package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.InspectionLicence;
import com.dstp.mapper.InspectionLicenceMapper;
import com.dstp.mapper.InspectionMapper;
import com.dstp.model.Inspection;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class InspectionHandler extends MessageHandler {
    private final InspectionMapper inspectionMapper;
    private final InspectionLicenceMapper inspectionLicenceMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public void process(JsonNode dataNode){
        List<Inspection> inspectionList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(inspectionList)) {
            log.warn("检务数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        inspectionList = distinct(inspectionList);

        // 1. 新增或更新检务信息
        inspectionMapper.batchUpsert(inspectionList);

        // 2. 查询已存在的检务信息ID
        List<Long> inspectionIds = batchGetParentPkId(inspectionList, inspectionMapper);

        // 3. 保存新检务单证信息
        List<InspectionLicence> inspectionLicenceList = batchSetManyChildFkId(inspectionList,
                Inspection::getLicenceList, InspectionLicence::setInspectionInfoId);
        // 删除旧检务单证信息
        inspectionLicenceMapper.delete(new QueryWrapper<InspectionLicence>().in("inspection_info_id", inspectionIds));
        // 保存新检务单证信息
        inspectionLicenceMapper.batchInsert(inspectionLicenceList);
    }
}
