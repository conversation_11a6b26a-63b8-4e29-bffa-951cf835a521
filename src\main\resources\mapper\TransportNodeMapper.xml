<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.TransportNodeMapper">
    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="transportNodeMap" type="com.dstp.model.TransportNode">
        <id property="id" column="id"/>
        <result property="transportId" column="transport_id"/>
        <result property="taskCode" column="task_code"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="transportNodeMap">
        SELECT id,transport_id,task_code
        FROM doc_transport_info_node
        WHERE (transport_id,task_code) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.transportId}, #{item.taskCode})
        </foreach>
    </select>
</mapper>