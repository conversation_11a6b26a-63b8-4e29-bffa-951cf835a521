package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 国内运输
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_internal_transport_order")
public class InternalTransport extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    private Integer type;
    private String transportMode;
    private String transportToolNo;
    private String transportTool;
    private String voyageNo;
    private String billNo;
    private String contractNo;
    private String invoiceNo;
    private String serviceType;
    private String provenance;
    private String destination;
    private String transferDestination;
    private String pickUpLocation;
    @NotNull(message = "港口托运时间不能为空")
    private LocalDateTime laodTime;
    private LocalDateTime agreedTimePick;
    private LocalDateTime agreedTimeOfService;
    private LocalDateTime uponTime;
    private LocalDateTime signTime;
    private LocalDateTime arrivalTime;
    private String vehicleNo;
    private String vehicleType;
    private String vehicleSpec;
    private String carpool;
    private String logisticsCode;
    private String logisticsName;
    private String logisticsNo;
    private String serviceOrderNo;
    private String busiOrderNo;
    @NotBlank(message = "企业订单号不能为空")
    private String entOrderNo;
    private String clientOrderNo;
    private String superviseType;
    private String commodity;
    private String placeOfLoading;
    private String placeOfUnloading;
    private BigDecimal splint;
    private Long sourceEntId;
    private Long serverEntId;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    private String linkId;
    @TableField(exist = false, select = false)
    private List<InternalTransportContainer> containerList;

    public String getUniqueKeyString() {
        if (entId == null || entOrderNo == null || sourceSite == null) {
            return null;
        }
        return entId + ":" + entOrderNo + ":" + sourceSite;
    }
}