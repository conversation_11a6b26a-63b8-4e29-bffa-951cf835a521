package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

/**
 * 仓库信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_warehouse")
public class Warehouse extends BaseEntity  {
    @TableId(type = IdType.AUTO)
    private Long id; // 主键自增
    private String warehouseId; // 仓库ID
    private String warehouseName; // 仓库名称
    private int bondedType; // 保税类型：0非保税，1保税
}
