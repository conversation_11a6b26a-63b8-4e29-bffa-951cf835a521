package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyLongDeserializer;
import com.dstp.deserializer.SizeDeserializer;
import com.dstp.deserializer.VolumeDeserializer;
import com.dstp.deserializer.WeightDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 理货商品
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_tally_goods")
public class TallyGoods extends BaseEntity {
    @NotNull(message = "理货单ID不能为空")
    private Long tallyOrderId; // 关联字段：doc_tally_header.id
    @JsonProperty("gNum")
    private String gNum; // 序号
    private String bookItemNo; // 账册项号
    @JsonProperty("goodsNo")
    private String goodNo; // 货号
    @JsonProperty("barCode")
    private String barcode; // 条码
    private String entGoodsNo; // 企业商品货号
    @JsonProperty("goodsName")
    private String goodName; // 商品名称
    private String entGoodsName; // 企业商品名称
    @JsonProperty("hsCode")
    private String hscode; // hscode 编码
    private String specification; // 规格型号
    @JsonDeserialize(using = MoneyLongDeserializer.class)
    private Long totalDeclarePrice; // 申报总金额（分）
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal grossWeight; // 总毛重（单位：克）
    @JsonDeserialize(using = WeightDeserializer.class)
    private BigDecimal netWeight; // 总净重（单位：克）
    private LocalDateTime productDate; // 生产日期
    private String lotNo; // 生产批次号
    @JsonProperty("expiryDate")
    private LocalDateTime expireDate; // 失效日期
    private BigDecimal expectReceiveQty; // 预计入库数量
    private String unit; // 计量单位
    private BigDecimal actReceiveQty; // 实际理货数量
    private BigDecimal goodQty; // 好品数量
    private BigDecimal damageQty; // 坏品数量
    private BigDecimal trayQty; // 托盘数
    @JsonDeserialize(using = SizeDeserializer.class)
    private BigDecimal length; // 长（cm）
    @JsonDeserialize(using = SizeDeserializer.class)
    private BigDecimal width; // 宽（cm）
    @JsonDeserialize(using = SizeDeserializer.class)
    private BigDecimal height; // 高（cm）
    @JsonDeserialize(using = VolumeDeserializer.class)
    private BigDecimal volume; // 体积（cm³）
    private String tallyBy; // 理货人
    private String auditBy; // 审核人
    private String acceptBy; // 验收人
    private String shelfBy; // 上架人
    private BigDecimal lackQty; // 缺失数量
    private BigDecimal overflowQty; // 多货数量
    private BigDecimal availableQty; // 可售数量
}
