package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 进出口物流单据-时间信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_import_export_logistics_time")
public class ImportExportLogisticsTime extends BaseEntity {
    @NotNull(message = "物流单ID不能为空")
    private Long documentId;
    @NotNull(message = "时间类型不能为空")
    private Integer timeType;
    @NotNull(message = "时间开始不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
