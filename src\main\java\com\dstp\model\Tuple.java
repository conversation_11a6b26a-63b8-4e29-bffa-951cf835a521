package com.dstp.model;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;
import java.util.Optional;

/**
 * Tuple基类，所有具体Tuple类型的抽象父类
 */
public abstract class Tuple implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 获取元组的元素数量
     * @return 元素数量
     */
    public abstract int getArity();

    /**
     * 获取指定位置的元素
     * @param pos 位置索引，从0开始
     * @return 元素值
     */
    public abstract <T> T getField(int pos);

    /**
     * 设置指定位置的元素
     * @param pos 位置索引，从0开始
     * @param value 元素值
     */
    protected abstract <T> void setField(int pos, T value);

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("(");
        for (int i = 0; i < getArity(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(Optional.ofNullable(getField(i)));
        }
        sb.append(")");
        return sb.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Tuple tuple = (Tuple) o;
        if (getArity() != tuple.getArity()) return false;

        for (int i = 0; i < getArity(); i++) {
            if (!Objects.equals(getField(i), tuple.getField(i))) {
                return false;
            }
        }
        return true;
    }

    @Override
    public int hashCode() {
        int result = 31 * getArity();
        for (int i = 0; i < getArity(); i++) {
            result = 31 * result + (getField(i) != null ? getField(i).hashCode() : 0);
        }
        return result;
    }
}
