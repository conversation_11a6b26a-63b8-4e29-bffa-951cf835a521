<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dstp.mapper.TallyMapper">
    <!-- 根据唯一索引查询主键ID -->
    <resultMap id="tallyMap" type="com.dstp.model.Tally">
        <id property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="sourceSite" column="source_site"/>
    </resultMap>
    <select id="selectByUniqueKeys" resultMap="tallyMap">
        SELECT id,ent_id,order_no,source_site
        FROM doc_tally_header
        WHERE (ent_id,order_no,source_site) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.entId}, #{item.orderNo}, #{item.sourceSite})
        </foreach>
    </select>
</mapper>