package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 结算信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_settlement_info")
public class Settlement extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    @NotBlank(message = "结算单号不能为空")
    private String orderNo;
    @NotNull(message = "账单日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate billDate;
    @NotBlank(message = "结算类型不能为空")
    private String billType;
    @NotBlank(message = "结算状态不能为空")
    private String billStatus;
    @NotNull(message = "应结算金额不能为空")
    private BigDecimal amount;
    private String currency;
    private String merchant;
    private String payerName;
    private String payerBank;
    private String payerAccount;
    private String payerArea;
    private String payeeName;
    private String payeeBank;
    private String payeeAccount;
    private String payeeArea;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    @TableField(exist = false, select = false)
    private List<SettlementInfoBill> billDetails; // 一对多关联：账单明细
    @TableField(exist = false, select = false)
    private List<SettlementInfoPay> payDetails; // 一对多关联：结算明细

    public String getUniqueKeyString() {
        if (entId == null || orderNo == null) {
            return null;
        }
        return entId + ":" + orderNo;
    }
}
