package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 结算信息-结算明细
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_settlement_info_pay")
public class SettlementInfoPay extends BaseEntity {
    @NotNull(message = "结算单ID不能为空")
    private Long settlementId; // 关联字段：doc_settlement_info.id
    private String serialNo;
    @NotNull(message = "结算日期不能为空")
    private LocalDateTime payDate;
    @NotNull(message = "结算金额不能为空")
    private BigDecimal amount;
}
