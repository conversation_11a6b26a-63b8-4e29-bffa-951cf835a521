package com.dstp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dstp.model.InternalTransportContainer;
import com.dstp.mapper.InternalTransportContainerMapper;
import com.dstp.mapper.InternalTransportMapper;
import com.dstp.model.InternalTransport;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class InternalTransportHandler extends MessageHandler {
    private final InternalTransportMapper internalTransportMapper;
    private final InternalTransportContainerMapper internalTransportContainerMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<InternalTransport> internalTransportList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(internalTransportList)) {
            log.warn("国内运输数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        internalTransportList = distinct(internalTransportList);

        // 1. 新增或更新国内运输
        internalTransportList.forEach(internalTransport -> internalTransport.setType(20));
        internalTransportMapper.batchUpsert(internalTransportList);

        // 2. 查询已存在的国内运输ID
        List<Long> transportIds = batchGetParentPkId(internalTransportList, internalTransportMapper);

        // 3. 保存新国内运输货柜信息
        List<InternalTransportContainer> internalTransportContainerList = batchSetManyChildFkId(internalTransportList,
                InternalTransport::getContainerList, InternalTransportContainer::setOrderId);
        // 删除旧国内运输货柜信息
        internalTransportContainerMapper.delete(new QueryWrapper<InternalTransportContainer>().in("order_id", transportIds));
        // 保存新国内运输货柜信息
        internalTransportContainerMapper.batchInsert(internalTransportContainerList);
    }
}
