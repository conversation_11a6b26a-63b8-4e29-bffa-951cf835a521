package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.dstp.deserializer.MoneyDecimalDeserializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 授信信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_credit_plan")
public class CreditPlan extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    @NotBlank(message = "资金方不能为空")
    private String capital;           // 资金方
    @NotBlank(message = "产品类型不能为空")
    private String product;           // 产品类型
    private String applyNo;           // 银行审批流水号
    private BigDecimal totalLineCny;  // 授信额度（人民币，单位：分）
    @JsonProperty("totalLines")
    @JsonDeserialize(using = MoneyDecimalDeserializer.class)
    private BigDecimal totalLine;     // 授信额度（原币种，单位：分）
    @NotBlank(message = "授信币种不能为空")
    private String currency;          // 授信币种
    private String contractNo;        // 合同编号
    private String applyStatus;       // 业务状态
    private LocalDateTime timeStart;  // 授信起始日
    @NotNull(message = "授信截止日不能为空")
    private LocalDateTime timeLimit;  // 授信截止日
    private String bankCardNo;        // 提还款账号
    private BigDecimal interestRate;  // 年利率（带8位小数精度）
    private String externalApplyNo;   // 外部申请编号

    @Override
    public String getUniqueKeyString() {
        if (entId == null || capital == null || product == null || timeLimit == null) {
            return null;
        }
        return entId + ":" + capital + ":" + product + ":" + timeLimit;
    }
}