package com.dstp.service.impl;

import com.dstp.mapper.BookingSpaceMapper;
import com.dstp.model.BookingSpace;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class BookingSpaceHandler extends MessageHandler {
    private final BookingSpaceMapper bookingSpaceMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode) {
        // 如果有多条箱号数据，则需要重构数据结构，将订舱单根据箱号拆分成多条
        List<BookingSpace> bookingSpaceList = new ArrayList<>();
        if (dataNode instanceof ObjectNode) {
            bookingSpaceList.add(objectMapper.convertValue(dataNode, BookingSpace.class));
        } else if (dataNode instanceof ArrayNode) {
            bookingSpaceList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        }

        if (CollectionUtils.isEmpty(bookingSpaceList)) {
            log.warn("订舱数据为空，跳过处理！");
            return;
        }

        for (BookingSpace bookingSpace : bookingSpaceList) {
            bookingSpace.setDataSources("INTERFACE");

            // 如果订舱单号和提单号都为空，则不处理
            Long entId = bookingSpace.getEntId();
            String soNo = bookingSpace.getSoNo();
            String billNo = bookingSpace.getBillNo();
            String containerNo = bookingSpace.getContainerNo();
            if (entId == null || (soNo == null && billNo == null)) {
                log.warn("数据唯一索引中有字段为空，跳过处理！");
                continue;
            }

            // 查询订舱单号或提单号或箱号，如果存在记录，判断海运订阅状态和港区订阅状态
            BookingSpace oldBookingSpace = bookingSpaceMapper.selectByUnique(entId, soNo, billNo, containerNo);

            if (oldBookingSpace != null) {
                // 企业数据中心V3.8.21 海运订阅状态和港区订阅状态都是成功，不再更新数据
                String portState = oldBookingSpace.getPortState();
                String shippingState = oldBookingSpace.getShippingState();
                if (!StringUtils.isAllBlank(portState, shippingState) && portState.equals("SUCCESS") && shippingState.equals("SUCCESS")) {
                    log.warn("原数据海运订阅状态和港区订阅状态都是成功，不再更新，跳过处理！");
                } else {
                    bookingSpaceMapper.updateByUnique(bookingSpace);
                }
            } else {
                // 如果不存在记录，则插入一条新记录
                bookingSpaceMapper.insert(bookingSpace);
            }
        }
    }
}
