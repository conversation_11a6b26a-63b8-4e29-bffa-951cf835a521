package com.dstp.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报关清单
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("doc_customs_clearance_inventory")
public class CustomsInventory extends BaseEntity {
    @NotNull(message = "企业ID不能为空")
    private Long entId;
    @NotBlank(message = "清单编号不能为空")
    private String invtNo;  // 清单编号（唯一索引字段）
    private String entOrderNo;
    @JsonProperty("electricCode")
    private String electricrCode;
    @JsonProperty("electricName")
    private String electricrName;
    private String cbepcomCode;
    private String cbepcomName;
    @JsonProperty("warehouseId")
    private String warehourseId;  // 注意字段名拼写与数据库一致
    private String wayBillNo;
    private String logisticsName;
    private String logisticsCode;
    private LocalDateTime declTime;
    private String customsCode;
    private Integer buyerIdType;
    private String buyerIdNumber;
    private String buyerName;
    private String buyerTelephone;
    private String consigneeAddress;
    private String agentCode;
    private String agentName;
    private BigDecimal freight;       // 物流费用（保留5位小数）
    private BigDecimal insuredFee;    // 保价费用（保留5位小数）
    private String currency;
    private BigDecimal grossWeight;   // 毛重（公斤）
    private BigDecimal netWeight;     // 净重（公斤）
    private LocalDateTime ratifyDate;
    private LocalDateTime deliveryTime;
    @NotBlank(message = "来源站点不能为空")
    private String sourceSite;
    // 一对多关联商品信息
    @TableField(exist = false, select = false)
    @JsonProperty("goodList")
    private List<CustomsInventoryGoods> goodsList;

    @Override
    public String getUniqueKeyString() {
        if (entId == null || invtNo == null) {
            return null;
        }
        return entId + ":" + invtNo;
    }
}