## 构建镜像
docker build -t registry.cn-gdgz1.ctyun.cn/dstp/dxc_sync_gel:1.0 .

## 容器部署
docker run -it --name dxc_sync_gel -p 8080:8080 \
-e KAFKA_BOOTSTRAP_SERVERS="10.38.38.13:9094,10.38.38.117:9094,10.38.38.197:9094" \
-e KAFKA_USERNAME="nanobytes" \
-e KAFKA_PASSWORD="3KxK7PHaepo84f49PG" \
-e SPRING_DATASOURCE_URL="***************************************************************************************************************************" \
-e SPRING_DATASOURCE_USERNAME="cdm" \
-e SPRING_DATASOURCE_PASSWORD="P8NTy7Xk8E4<gi62Dk" \
-e WE_CHAT_WEBHOOK_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d0094a40-2ca8-409a-b748-3d35c6ac5169" \
registry.cn-gdgz1.ctyun.cn/dstp/dxc_sync_gel:1.0

## 上传镜像
docker push registry.cn-gdgz1.ctyun.cn/dstp/dxc_sync_gel:1.0