package com.dstp.service.impl;

import com.dstp.mapper.PodMapper;
import com.dstp.model.Pod;
import com.dstp.service.MessageHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class PodHandler extends MessageHandler {
    private final PodMapper podMapper;
    private final ObjectMapper objectMapper;

    @Transactional(rollbackFor = Exception.class)
    public void process(JsonNode dataNode){
        List<Pod> podList = objectMapper.convertValue(dataNode, new TypeReference<>() {});
        if (CollectionUtils.isEmpty(podList)) {
            log.warn("抵港数据为空，跳过处理！");
            return;
        }

        // 0. 去重
        podList = distinct(podList);

        // 1. 新增或更新货物到港信息
        podMapper.batchUpsert(podList);
    }
}
